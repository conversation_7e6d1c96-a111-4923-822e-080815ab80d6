from datetime import datetime, timedelta
from django.utils import timezone
import json
from venv import logger
from django.forms import model_to_dict
from django.http import JsonResponse
from django.shortcuts import redirect
from django.core.mail import send_mail
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.serializers.json import DjangoJSONEncoder
from django.forms.models import model_to_dict
from django.db.models.fields.files import FieldFile
from django.db.models.fields.files import FieldFile
from django.views.decorators.csrf import csrf_exempt

import requests
from rest_framework.views import APIView
from rest_framework.response import Response

from rest_framework import status

from django.db.models import Q, Count , Prefetch , Exists,OuterRef

from user_agents import parse
from Authentication.jwt_auth import CustomJWTAuthentication
from RazorPay.payments import create_payment_link, get_payment_status
from TikTok.tiktok_auth import tiktok_auth_url, tiktok_code_token
from Vimeo.auth_vimeo import code_to_token_vimeo, get_user_url_vimeo
from Vimeo.get_vimeo_profile import get_vimeo_profile_for_mulitple
from core import settings
from helpers.dollar_to_inr import fun_dollar_to_inr
from helpers.check_availability import check_multiple_roles_permissions, check_user_plan
from helpers.email_regex import is_valid_email
from helpers.extract_permission import extract_permission_keys
from helpers.id_decode import decode_token
from helpers.image_h_w import get_image_dimensions
from helpers.login_activity import has_logged_in_today, has_seven_day_streak
from helpers.onesignal_test import send_notification
from helpers.otp import generate_otp
from helpers.paginator import UserProfilePagination
from helpers.profile_completion import get_profile_completion_status
from helpers.refresh_to_access import get_access_token_from_refresh_token
from helpers.temp_password import generate_temp_password
from helpers.utf_to_string import decode_url
from helpers.week_calculator import get_week_dates_str
from linkedin.linkedin_auth import code_to_token_lin, generate_url, get_profile
from meta.facebook_auth import facebook_code_to_token, generate_facebook_auth_url, generate_facebook_auth_url_dev
from meta.instagram_auth import generate_instagram_url, generate_instagram_url_dev
from meta.threads_auth import generate_threads_url, threads_after_auth
from mongodb.db import *
from pinterest.get_pin_profile import get_pinterest_profile_for_multiple
from pinterest.pin_auth import get_user_url_pinterest, pin_code_to_token
from reddit.reddit_auth import reddit_auth_url, reddit_authorise
from reddit.reddit_profile import get_reddit_profile_multiple
from telegram.telegram_auth import send_telegram_code, telegram_sign_in
from youtube.google_auth import authenticate_user, get_auth_url
from youtube.youtube_stats import get_channel_id, get_channel_id_without_access_token
from mastodon.mastodon_auth import (
    register_mastodon_app, generate_mastodon_auth_url, exchange_code_for_token,
    get_mastodon_user_info
)
from ghost.ghost_auth import validate_ghost_credentials
from ghost.ghost_analytics import get_ghost_analytics

from .serializers import *
from helpers.custom_hasher import custom_hasher
from helpers.generate_jwt import generate_jwt

from .models import *
from Project.models import *
from Project.serializers import *
from core.settings import *

from requests_oauthlib import OAuth1
import requests
from urllib.parse import parse_qs

from helpers.subscription_id import *


import time
import uuid
import base64
import requests
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

def host(request):
    return request.get_host()


class UserRegisterView(APIView):
    @sentry_sdk.trace
    def post(self, request):
        try:
            serializer = UserRegisterSerializer(data=request.data)
            if serializer.is_valid():
                name = request.data.get('name')
                username = request.data.get('username')
                email = request.data.get('email')
                password = request.data.get('password')
                onesignal_player = request.data.get('onesignal_id')
                refference_code = request.data.get('refference_code')
                verify_email = custom_hasher(email)
                check_email = is_valid_email(email)
                if check_email == False:
                    return Response({'status': False, 'message': 'Please Enter A Valid Email'}, status=status.HTTP_400_BAD_REQUEST)
                if not username:
                    username = ''
                
                if refference_code:
                    try:
                        get_refference = UserRegistration.objects.get(refference_code=refference_code)
                        if get_refference.is_deleted == True:
                            return Response({'status': False, 'message': 'Invalid Referral Code'}, status=status.HTTP_400_BAD_REQUEST)
                    except UserRegistration.DoesNotExist:
                        return Response({'status': False, 'message': 'Invalid Referral Code'}, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    get_user = UserRegistration.objects.get(
                        Q(email=verify_email))
                    if get_user.is_deleted == True:
                        get_user.delete()
                        create = UserRegistration.objects.create(
                            name=name,
                            username=username,
                            email=custom_hasher(str(email).lower()),
                            enc_email=str(email).lower(),
                            password=custom_hasher(password),
                            phone='',
                            verify_phone=custom_hasher('phone'),
                            onesignal_player=onesignal_player
                        )
                        SurveyForm.objects.create(user_id=create.pk,name=name)
                        UserSubscription.objects.create(user_id=create.pk,subscription_id=free_subscription_id)
                        token = generate_jwt(create)
                        create_brand = Brands.objects.create(
                            user_id=create.pk,
                            name=name,
                            email=email,
                        )
                        CurrentBrand.objects.create(user_id=create.pk,brand_id=create_brand.pk)
                        db_create_user_social(create_brand.pk)
                        db_create_user_points(create.pk)
                        create_third_party = ThirdPartyAuth.objects.create(
                            brand_id=create_brand.pk)
                        facebook = False
                        instagram = create_third_party.instagram_check
                        linkedin = create_third_party.linkedin_check
                        pinterest = create_third_party.pinterest_check
                        vimeo = create_third_party.vimeo_check
                        tumblr = create_third_party.tumblr_check
                        tiktok = create_third_party.tiktok_check
                        threads = create_third_party.thread_check
                        reddit = create_third_party.reddit_check
                        youtube = create_third_party.youtube_check
                        telegram = create_third_party.telegram_check
                        mastodon = create_third_party.mastodon_check
                        twitter = False
                        dailymotion = False
                        x = create_third_party.x_check
                        return Response({'status': True, 'message': 'User Registered Successfully', 'data': {
                            'user_status':create.current_status,'user_id': create.pk, 'username': username, 'name': name, 'email': email, 'phone': '', 'profile_image': '', 'token': token
                        }, 'Facebook': facebook, 'Instagram': instagram, 'x': x, 'YouTube': youtube, 'LinkedIn': linkedin, 'Pinterest': pinterest, 'tiktok': tiktok, 'threads': threads, "Twitter":twitter,'Dailymotion': dailymotion, 'Vimeo': vimeo,'telegram':telegram,'mastodon':mastodon, 'tumblr': tumblr, 'reddit': reddit}, status=status.HTTP_200_OK)
                    return Response({'status': True, 'message': 'User Already Registered Please Try Diffrent Email'}, status=status.HTTP_400_BAD_REQUEST)
                except UserRegistration.DoesNotExist:
                    create = UserRegistration.objects.create(
                        name=name,
                        username=username,
                        email=custom_hasher(str(email).lower()),
                        enc_email=str(email).lower(),
                        password=custom_hasher(password),
                        phone='',
                        verify_phone=custom_hasher('phone'),
                        onesignal_player=onesignal_player
                    )
                    SurveyForm.objects.create(user_id=create.pk,name=name)
                    UserSubscription.objects.create(user_id=create.pk,subscription_id=free_subscription_id)
                    token = generate_jwt(create)
                    create_brand = Brands.objects.create(
                            user_id=create.pk,
                            name=name,
                            email=email,
                    )
                    CurrentBrand.objects.create(user_id=create.pk,brand_id=create_brand.pk)
                    db_create_user_social(create_brand.pk)
                    db_create_user_points(create.pk)
                    create_third_party = ThirdPartyAuth.objects.create(
                        brand_id=create_brand.pk)
                    
                    facebook = False
                    instagram = create_third_party.instagram_check
                    linkedin = create_third_party.linkedin_check
                    pinterest = create_third_party.pinterest_check
                    vimeo = create_third_party.vimeo_check
                    tumblr = create_third_party.tumblr_check
                    reddit = create_third_party.reddit_check
                    tiktok = create_third_party.tiktok_check
                    threads = create_third_party.thread_check
                    youtube = create_third_party.youtube_check
                    telegram = create_third_party.telegram_check
                    mastodon = create_third_party.mastodon_check
                    dailymotion = False
                    twitter = False
                    X = create_third_party.x_check
                    if refference_code:
                        try:
                            invitee = UserRegistration.objects.get(refference_code=refference_code)
                            create_refference = Reffrence.objects.create(invitee_id=invitee.pk,invited_id=create.pk)
                        except UserRegistration.DoesNotExist:
                            pass
                    return Response({'status': True, 'message': 'User Registered Successfully', 'data': {
                        'user_status':create.current_status,'user_id': create.pk, 'username': username, 'name': name, 'email': email, 'phone': '', 'profile_image': '', 'token': token
                    }, 'Facebook': facebook, 'Instagram': instagram, 'x': X, 'YouTube': youtube, 'LinkedIn': linkedin, "Twitter":twitter,'Pinterest': pinterest, 'tiktok': tiktok,'telegram':telegram,'mastodon':mastodon,'threads': threads, 'Dailymotion': dailymotion, 'Vimeo': vimeo, 'tumblr': tumblr, 'reddit': reddit}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class UserIndustryView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            industry = request.data.get('industry')
            type = request.data.get('type')
            try:
                get_user = UserRegistration.objects.get(pk=user_id)
                get_user.user_industry = industry
                get_user.user_type = type
                get_user.current_status = '3'
                get_user.save()
                brand = Brands.objects.filter(user_id=user_id).first()
                return Response({'status': True, 'message': 'User Industry & User Type Updated Successfully','user_status':get_user.current_status,'brand_id':brand.pk}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class UserTypeSwitchView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            type = request.data.get('type')
            try:
                get_user = UserRegistration.objects.get(pk=user_id)
                get_user.user_type = type
                get_user.save()
                brand = Brands.objects.filter(user_id=user_id).first()
                return Response({'status': True, 'message': 'User Type Updated Successfully','user_status':get_user.current_status,'brand_id':brand.pk}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class UserLoginView(APIView):
    @sentry_sdk.trace
    def post(self, request):
        user_agent = request.headers.get('User-Agent')
        print(user_agent)
        try:
            creds = str(request.data.get('creds')).lower()
            password = request.data.get('password')
            if not creds or not password:
                return Response({'status': False, 'message': 'Login credential required'}, status=status.HTTP_400_BAD_REQUEST)
            # verify_creds = custom_hasher(creds)
            verify_password = custom_hasher(password)
            onesignal_player = request.data.get('onesignal_id')
            brand_id = ''

            try:
                get_user = UserRegistration.objects.get(
                    Q(enc_email=creds) | Q(verify_phone=creds))
                # get_subscription = UserSubscription.objects.get(user_id=get_user.pk)
                if get_user.is_deleted == True:
                    return Response({'status': False, 'message': 'Your Account Is Deleted'}, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    current_brand_obj = CurrentBrand.objects.get(user_id=get_user.pk)
                    third_party_data = ThirdPartyAuth.objects.get(
                        brand_id=current_brand_obj.brand.pk)
                except CurrentBrand.DoesNotExist:
                     third_party_data = ThirdPartyAuth.objects.filter(
                        brand__user=get_user).first()

                facebook = third_party_data.facebook_check
                instagram = third_party_data.instagram_check
                linkedin = third_party_data.linkedin_check
                pinterest = third_party_data.pinterest_check
                vimeo = third_party_data.vimeo_check
                tumblr = third_party_data.tumblr_check
                reddit = third_party_data.reddit_check
                tiktok = third_party_data.tiktok_check
                threads = third_party_data.thread_check
                youtube = third_party_data.youtube_check
                telegram = third_party_data.telegram_check
                mastodon = third_party_data.mastodon_check
                twitter = False
                dailymotion = False
                X = third_party_data.x_check
                check_list = [facebook, instagram, linkedin,
                                  pinterest, vimeo, youtube, dailymotion, X]
                is_any_auth = False
                for i in check_list:
                        if i == True:
                            is_any_auth = True
                        else:
                            pass

                check_list = [facebook, instagram, linkedin,
                              pinterest, vimeo, youtube, dailymotion, X]
                is_any_auth = False
                for i in check_list:
                    if i == True:
                        is_any_auth = True
                    else:
                        pass
                try:
                    domain = request.get_host()
                    if '127.0.0.1' in domain:
                        header = 'https://'
                    else:
                        header = 'https://'
                    profile_picture = f'{header}{domain}{get_user.profile_picture.url}'
                except ValueError:
                    profile_picture = ''
                    header = ''
                    domain = ''

                if get_user.password == verify_password or get_user.temp_password == password:
                    token = generate_jwt(get_user)
                    if onesignal_player:
                        get_user.onesignal_player.append(onesignal_player)
                    get_user.save()
                    # subscription_data = check_user_plan(get_user.pk,get_subscription.subscription.pk)
                    return Response({'status': True,'device':f'{user_agent}','message': 'Sign In Successfully', 'user_status':get_user.current_status,'user_id': get_user.pk,'brand_id':third_party_data.brand.pk, 'username': get_user.username, 'name': get_user.name, 'profile_image': profile_picture, 'token': token, 'is_any_auth': is_any_auth, 'Facebook': facebook, 'Instagram': instagram, 'x': X, 'YouTube': youtube, 'LinkedIn': linkedin, 'Pinterest': pinterest, 'tiktok': tiktok, 'threads': threads, 'Dailymotion': dailymotion, "Twitter":twitter,'tumblr': tumblr, 'Vimeo': vimeo, 'telegram':telegram,'mastodon':mastodon,'reddit': reddit, 'is_admin': get_user.is_admin,'sub_data':{}}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'Password Is Incorrect'}, status=status.HTTP_400_BAD_REQUEST)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'Please Sign Up Before Sign In'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserLogiSwitchnView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        token = request.headers.get('Authorization')
        user_id = decode_token(token)

        try:
            creds = str(request.data.get('creds')).lower()
            password = request.data.get('password')
            if not creds or not password:
                return Response({'status': False, 'message': 'Login credential required'}, status=status.HTTP_400_BAD_REQUEST)
            # verify_creds = custom_hasher(creds)
            verify_password = custom_hasher(password)
            onesignal_player = request.data.get('onesignal_id')
            brand_id = ''

            try:
                get_user = UserRegistration.objects.get(
                    Q(enc_email=creds))
                # get_subscription = UserSubscription.objects.get(user_id=get_user.pk)
                if get_user.is_deleted == True:
                    return Response({'status': False, 'message': 'Your Account Is Deleted'}, status=status.HTTP_400_BAD_REQUEST)
                
                if get_user.temp_password != password:
                    return Response({'status': False, 'message': 'Password Is Incorrect'}, status=status.HTTP_400_BAD_REQUEST)

                try:
                    current_brand_obj = CurrentBrand.objects.get(user_id=get_user.pk)
                    third_party_data = ThirdPartyAuth.objects.get(
                        brand_id=current_brand_obj.brand.pk)
                    if user_id not in current_brand_obj.brand.user_list:
                        return Response({'status': False, 'message': 'You are not authorized to access this Account'}, status=status.HTTP_400_BAD_REQUEST)
                except CurrentBrand.DoesNotExist:
                    third_party_data = ThirdPartyAuth.objects.filter(
                        brand__user=get_user).first()
                    if user_id not in third_party_data.brand.user_list:
                        return Response({'status': False, 'message': 'You are not authorized to access this Account'}, status=status.HTTP_400_BAD_REQUEST)

                facebook = third_party_data.facebook_check
                instagram = third_party_data.instagram_check
                linkedin = third_party_data.linkedin_check
                pinterest = third_party_data.pinterest_check
                vimeo = third_party_data.vimeo_check
                tumblr = third_party_data.tumblr_check
                reddit = third_party_data.reddit_check
                tiktok = third_party_data.tiktok_check
                threads = third_party_data.thread_check
                youtube = third_party_data.youtube_check
                telegram = third_party_data.telegram_check
                twitter = False
                dailymotion = False
                X = third_party_data.x_check
                check_list = [facebook, instagram, linkedin,
                                  pinterest, vimeo, youtube, dailymotion, X]
                is_any_auth = False
                for i in check_list:
                        if i == True:
                            is_any_auth = True
                        else:
                            pass

                check_list = [facebook, instagram, linkedin,
                              pinterest, vimeo, youtube, dailymotion, X]
                is_any_auth = False
                for i in check_list:
                    if i == True:
                        is_any_auth = True
                    else:
                        pass
                try:
                    domain = request.get_host()
                    if '127.0.0.1' in domain:
                        header = 'https://'
                    else:
                        header = 'https://'
                    profile_picture = f'{header}{domain}{get_user.profile_picture.url}'
                except ValueError:
                    profile_picture = ''
                    header = ''
                    domain = ''

                if get_user.temp_password == password:
                    token = generate_jwt(get_user)
                    if onesignal_player:
                        get_user.onesignal_player.append(onesignal_player)
                    get_user.save()
                    # subscription_data = check_user_plan(get_user.pk,get_subscription.subscription.pk)
                    return Response({'status': True,'message': 'Sign In Successfully', 'user_status':get_user.current_status,'user_id': get_user.pk,'brand_id':third_party_data.brand.pk, 'username': get_user.username, 'name': get_user.name, 'profile_image': profile_picture, 'token': token, 'is_any_auth': is_any_auth, 'Facebook': facebook, 'Instagram': instagram, 'x': X, 'YouTube': youtube, 'LinkedIn': linkedin, 'Pinterest': pinterest, 'tiktok': tiktok, 'threads': threads, 'Dailymotion': dailymotion, "Twitter":twitter,'tumblr': tumblr, 'Vimeo': vimeo, 'telegram':telegram,'mastodon':False,'reddit': reddit, 'is_admin': get_user.is_admin,'sub_data':{}}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'Password Is Incorrect'}, status=status.HTTP_400_BAD_REQUEST)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'Please Sign Up Before Sign In'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserForgotPasswordView(APIView):
    @sentry_sdk.trace
    def post(self, request):
        try:
            email = request.data.get('email')
            verify_email = custom_hasher(email)
            try:
                get_user = UserRegistration.objects.get(Q(enc_email=email),Q(is_deleted=False))
                otp = generate_otp()
                # otp = '000000'
                get_user.otp = custom_hasher(otp)
                get_user.save()
                send_mail('Reset Password OTP',f'Your One Time Password For Resetting Password Is : {otp}',EMAIL_HOST_USER,[email])
                return Response({'status': True, 'message': 'OTP sent to your email address. Kindly check to continue.'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User With Given Email Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GetThirdParty(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            third_party_data = ThirdPartyAuth.objects.get(
                brand_id=brand_id)
            facebook = third_party_data.facebook_check
            instagram = third_party_data.instagram_check
            linkedin = third_party_data.linkedin_check
            pinterest = third_party_data.pinterest_check
            vimeo = third_party_data.vimeo_check
            tumblr = third_party_data.tumblr_check
            reddit = third_party_data.reddit_check
            tiktok = third_party_data.tiktok_check
            threads = third_party_data.thread_check
            youtube = third_party_data.youtube_check
            telegram = third_party_data.telegram_check
            mastodon = third_party_data.mastodon_check
            ghost = third_party_data.ghost_check
            dailymotion = False
            x = third_party_data.x_check
            check_list = [facebook, instagram, linkedin,
                          pinterest, vimeo, youtube, dailymotion, x, ghost]
            is_any_auth = False
            for i in check_list:
                if i == True:
                    is_any_auth = True
                else:
                    pass
        except ThirdPartyAuth.DoesNotExist:
            create = ThirdPartyAuth.objects.create(brand_id=brand_id)
            facebook = False
            instagram = create.instagram_check
            linkedin = create.linkedin_check
            pinterest = create.pinterest_check
            vimeo = create.vimeo_check
            tumblr = create.tumblr_check
            reddit = create.reddit_check
            tiktok = create.tiktok_check
            threads = create.thread_check
            youtube = create.youtube_check
            telegram = create.telegram_check
            dailymotion = False
            mastodon = create.mastodon_check
            ghost = create.ghost_check
            x = create.x_check
        return Response({'status': True, 'message': 'Data found successfully', 'data': {'is_any_auth': is_any_auth, 'Facebook': facebook,'telegram':telegram,'mastodon':mastodon,'ghost':ghost,'Instagram': instagram, 'x': x, 'YouTube': youtube, 'LinkedIn': linkedin, 'Pinterest': pinterest, 'tiktok': tiktok, 'threads': threads, 'Dailymotion': dailymotion, 'tumblr': tumblr, 'Vimeo': vimeo, 'reddit': reddit}}, status=status.HTTP_200_OK)


class UserVerifyOtpView(APIView):
    @sentry_sdk.trace
    def post(self, request):
        try:
            email = request.data.get('email')
            user_otp = request.data.get('user_otp')
            verify_otp = custom_hasher(user_otp)
            verify_email = custom_hasher(email)
            try:
                get_user = UserRegistration.objects.get(email=verify_email)
                if get_user.otp == verify_otp:
                    get_user.otp = ''
                    get_user.save()
                    token = generate_jwt(get_user)
                    return Response({'status': True, 'message': 'OTP Verified Sucessfully', 'token': token}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'OTP Is Invalid'}, status=status.HTTP_400_BAD_REQUEST)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User With Given Email Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserNewPasswordView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            new_password = request.data.get('new_password')
            try:
                get_user = UserRegistration.objects.get(pk=user_id)
                get_user.password = custom_hasher(new_password)
                get_user.save()
                return Response({'status': True, 'message': 'Password Reset Was Successfull'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User With Given Email Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class RedditAuthorizationUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = brand_id
        url = reddit_auth_url(user_id)
        return Response({'status': True, 'message': 'Reddit Url Generated','url': url}, status=status.HTTP_200_OK)


class RedditRedirectView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        authorization_code = request.GET.get('code')
        brand_id = request.GET.get('state')
        try:
            auth_token = reddit_authorise(authorization_code)
            profile = get_reddit_profile_multiple(auth_token)
            user = ThirdPartyAuth.objects.get(brand_id=brand_id)
            user.reddit_check = True
            user.reddit_token = auth_token
            user.reddit_username = profile
            user.save()
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except Exception:
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')


class RedditAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            authorization_code = request.data.get('authorization_code')
            auth_token = reddit_authorise(authorization_code)
            user = ThirdPartyAuth.objects.get(user_id=user_id)
            user.reddit_check = True
            user.reddit_token = auth_token
            user.save()
            return Response({'status': True, 'message': 'Reddit authentication completed successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except KeyError:
            return Response({'status': False, 'message': 'Reddit Authentication Failed'}, status=status.HTTP_400_BAD_REQUEST)


class GetAuthorizationURL(APIView):
    """Returns the authorization URL for user to grant access."""
    @sentry_sdk.trace

    def get(self, request):
        try:
            # Step 1: Get a request token
            oauth = OAuth1(CONSUMER_KEY, client_secret=CONSUMER_SECRET)
            print(oauth)
            response = requests.post(REQUEST_TOKEN_URL, auth=oauth)
            print(response.text)
            if response.status_code != 200:
                return Response({'error': 'Failed to obtain request token'}, status=status.HTTP_400_BAD_REQUEST)

            request_token = dict(parse_qs(response.content.decode('utf-8')))
            oauth_token = request_token['oauth_token'][0]
            oauth_token_secret = request_token['oauth_token_secret'][0]

            # Step 2: Construct the authorization URL
            authorization_url = f"{AUTHORIZE_URL}?oauth_token={oauth_token}"

            return Response({'status': True, 'authorization_url': authorization_url, 'oauth_token': oauth_token, 'oauth_token_secret': oauth_token_secret}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetTumblrRedirect(APIView):
    """Returns the authorization URL for user to grant access."""
    @sentry_sdk.trace

    def get(self, request):
        try:
            token_verifier = request.GET.get('oauth_verifier')
            return JsonResponse({'status': True, 'message': 'Token generated successfully', 'token_verifier': token_verifier})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetAccessToken(APIView):
    """Exchanges the oauth_verifier for access tokens."""
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            # user_id = decode_token(token)
            oauth_token = request.data.get('oauth_token')
            oauth_token_secret = request.data.get('oauth_token_secret')
            oauth_verifier = request.data.get('oauth_verifier')
            access_token_url = 'https://www.tumblr.com/oauth/access_token'

            if not oauth_token or not oauth_token_secret or not oauth_verifier:
                return Response({'error': 'Missing required parameters'}, status=status.HTTP_400_BAD_REQUEST)

            # Exchange oauth_verifier for access tokens
            oauth_access = OAuth1(
                CONSUMER_KEY,
                client_secret=CONSUMER_SECRET,
                resource_owner_key=oauth_token,
                resource_owner_secret=oauth_token_secret,
                verifier=oauth_verifier
            )

            response = requests.post(access_token_url, auth=oauth_access)

            if response.status_code != 200:
                return Response({'error': 'Failed to obtain access token'}, status=status.HTTP_400_BAD_REQUEST)

            # Parse the response and return access tokens
            access_token_data = dict(
                parse_qs(response.content.decode('utf-8')))
            user = ThirdPartyAuth.objects.get(brand_id=brand_id)
            user.tumblr_check = True
            user.tumbler_token = access_token_data['oauth_token'][0]
            user.tumbler_secret = access_token_data['oauth_token_secret'][0]
            user.save()
            return Response({'status': True, 'message': 'Brand Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class InstagramUrlView(APIView):
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user = decode_token(auth_token)
        if logged_in_user == 59 or logged_in_user == 1:
            brand_id = request.headers.get('brand')
            user_id = brand_id
            url = generate_instagram_url_dev(user_id)
        else:
            brand_id = request.headers.get('brand')
            user_id = brand_id
            url = generate_instagram_url(user_id)
        return Response({'status': True, 'message': 'Instagram Url Generated', 'url': url}, status=status.HTTP_200_OK)


class LinkedInUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        print(brand_id)
        user_id = brand_id
        url = generate_url(user_id)
        return Response({'status': True, 'message': 'LinkedIn Url Generated', 'url': url}, status=status.HTTP_200_OK)


class YoutubeAuthUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        token = request.headers.get('Authorization')
        user_id = request.headers.get('brand')
        url = get_auth_url(user_id)
        return Response({'status': True, 'message': 'YouTube Url Generated', 'url': url}, status=status.HTTP_200_OK)


class PinterestUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = brand_id
        url = get_user_url_pinterest(user_id)
        return Response({'status': True, 'message': 'Pinterest Url Generated', 'url': url}, status=status.HTTP_200_OK)


class LinkedInAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        data = request.data
        code = request.GET.get('code')
        brand_id = request.GET.get('state')
        token = code_to_token_lin(code)
        unique_id = get_profile(token)
        try:
            try:
                user_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_data.linkedin_check = True
                user_data.linkedin_creds = unique_id
                user_data.linked_in_token = token
                user_data.save()
                is_connected_once = db_get_user_social(brand_id,"linkedin")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,linkedin=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    brand_id=brand_id,
                    linkedin_check=True,
                    linkedin_creds=unique_id,
                    linked_in_token=token
                )
                is_connected_once = db_get_user_social(brand_id,"linkedin")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,linkedin=True)
                
                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')

        except Exception as e:
            return JsonResponse({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class YoutubeAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        data = request.data
        code = request.GET.get('code')
        state = request.GET.get('state')
        print(f'State = {state}')
        authenticate = authenticate_user(f'youtube/{state}', code)
        file_path = f'youtube/{state}-oauth2.json'
        with open(file_path, 'r') as file:
            credentials = json.load(file)
        refresh_token = credentials['refresh_token']
        get_data = ThirdPartyAuth.objects.get(brand_id=state)
        if authenticate:
            if refresh_token is not None:
                channel_id = get_channel_id(file_path,refresh_token)
            else:
                channel_id = get_channel_id_without_access_token(file_path)

            if channel_id != "" :
                get_data.youtube_check = True
                if refresh_token is not None:
                    get_data.youtube_refresh_token = refresh_token
                get_data.save()
                is_connected_once = db_get_user_social(state,"youtube")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(get_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(state,youtube=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{state}')
            else:
                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{state}')
        return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{state}')


class PinterestAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        code = request.GET.get('code')
        brand_id = request.GET.get('state')
        token = pin_code_to_token(code)
        unique_id = ''
        profile = get_pinterest_profile_for_multiple(token)
        try:
            try:
                user_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_data.pinterest_check = True
                user_data.pinterest_creds = token
                user_data.pinterest_username=profile
                user_data.save()
                is_connected_once = db_get_user_social(brand_id,"pinterest")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,pinterest=True)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    brand_id=brand_id,
                    pinterest_check=True,
                    pinterest_creds=unique_id,
                    pinterest_username=profile
                )
                is_connected_once = db_get_user_social(brand_id,"pinterest")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,pinterest=True)

            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except Exception as e:
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')


class VimeoUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id =brand_id
        url = get_user_url_vimeo(user_id)
        return Response({'status': True, 'message': 'Vimeo Url Generated', 'url': url}, status.HTTP_200_OK)


class VimeoAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        code = request.GET.get('code')
        brand_id = request.GET.get('state')
        token = code_to_token_vimeo(code)
        unique_id = ''
        profile = get_vimeo_profile_for_mulitple(token)
        try:
            try:
                user_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_data.vimeo_check = True
                user_data.vimeo_creds = token
                user_data.vimeo_username = profile
                user_data.save()
                is_connected_once = db_get_user_social(brand_id,"vimeo")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,vimeo=True)

            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    brand_id=brand_id,
                    vimeo_check=True,
                    vimeo_creds=unique_id,
                    vimeo_username=profile
                )
                is_connected_once = db_get_user_social(brand_id,"vimeo")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,vimeo=True)

            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except Exception as e:
            return JsonResponse({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LinkedInAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            token = request.data.get('token')
            unique_id = request.data.get('unique_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.linkedin_check = True
                user_data.linkedin_creds = unique_id
                user_data.linked_in_token = token
                user_data.save()
                return Response({'status': True, 'message': 'User Linked Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    linkedin_check=True,
                    linkedin_creds=unique_id,
                    linked_in_token=token
                )
                return Response({'status': True, 'message': 'User Linked Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class VimeoAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            token = request.data.get('token')
            unique_id = request.data.get('unique_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.vimeo_check = True
                user_data.vimeo_creds = token
                user_data.save()
                return Response({'status': True, 'message': 'User Vimeo Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    vimeo_check=True,
                    vimeo_creds=unique_id
                )
                return Response({'status': True, 'message': 'User Vimeo Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class PinterestAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            token = request.data.get('token')
            unique_id = request.data.get('unique_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.pinterest_check = True
                user_data.pinterest_creds = token
                user_data.save()
                return Response({'status': True, 'message': 'User Pinterest Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    pinterest_check=True,
                    pinterest_creds=unique_id
                )
                return Response({'status': True, 'message': 'User Pinterest Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserFacebookDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.facebook_check = False
            third_party_auth.facebook_creds = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Facebook services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserThreadDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')


            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.thread_check = False
            third_party_auth.thread_user_id = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Threads services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserTiktokDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            user = UserRegistration.objects.filter(id=user_id).first()

            third_party_auth = ThirdPartyAuth.objects.filter(user=user).first()
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this user'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.tiktok_check = False
            third_party_auth.tiktok_access_token = ''
            third_party_auth.tiktok_refresh_token = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'User disconnected from TikTok services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserInstagramDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.instagram_check = False
            third_party_auth.insta_auth_token = ''
            third_party_auth.insta_user_id = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Instagram services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserLinkedinDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.linkedin_check = False
            third_party_auth.linkedin_creds = ''
            third_party_auth.linked_in_token = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Linkedin services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserPinterestDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.pinterest_check = False
            third_party_auth.pinterest_creds = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Pinterest services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserVimeoDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.vimeo_check = False
            third_party_auth.vimeo_creds = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Vimeo services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UsertumblrDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.tumblr_check = False
            third_party_auth.tumbler_token = ''
            third_party_auth.tumbler_secret = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Tumblr services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserRedditDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this brand'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.reddit_check = False
            third_party_auth.reddit_token = ''
            third_party_auth.save()

            return Response({'status': True, 'message': 'Brand disconnected from Reddit services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserYoutubeDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')


            third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            if not third_party_auth:
                return Response({'status': False, 'message': 'No third-party data found for this user'}, status=status.HTTP_400_BAD_REQUEST)

            third_party_auth.youtube_check = False
            file_path = f"youtube/{brand_id}-oauth2.json"
            access_token = get_access_token_from_refresh_token(
                file_path, third_party_auth.youtube_refresh_token)
            disconnect = requests.post(
                f'https://oauth2.googleapis.com/revoke?token={access_token}')
            if disconnect.status_code == 200:
                if os.path.exists(file_path):
                    os.remove(file_path)
                third_party_auth.youtube_refresh_token = ''
                third_party_auth.save()
            else:
                third_party_auth.youtube_refresh_token = ''
                third_party_auth.save()
            return Response({'status': True, 'message': 'User disconnected from Youtube services successfully and data cleared.', 'Connect': False}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetUserProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            query_user_id = request.query_params.get('user_id')
            user_id = query_user_id if query_user_id is not None else token_user_id

            # Get domain info once
            try:
                domain = request.get_host()
                header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            # Use .only() to fetch only required fields
            try:
                user_data = UserRegistration.objects.only(
                    'id', 'name', 'username', 'enc_email', 'bio', 'dob', 'phone', 'country', 'state', 'city', 'gender', 'profile_picture', 'refference_code'
                ).get(pk=user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)

            # Profile image URL
            profile_picture = user_data.profile_picture.url if user_data.profile_picture else ''
            profile_image = f"{header}{domain}{profile_picture}" if profile_picture else ""

            # following_count = Follow.objects.filter(from_user_id=user_id).count()
            # followers_count = Follow.objects.filter(to_user_id=user_id).count()

            blocked_users = list(Block.objects.filter(from_user_id=user_id).values_list('to_user_id', flat=True))

            blocked_by_users = list(Block.objects.filter(to_user_id=user_id).values_list('from_user_id', flat=True))

            following_count = Follow.objects.filter(from_user_id=user_id).exclude(to_user_id__in=blocked_users + blocked_by_users).count()
            followers_count = Follow.objects.filter(to_user_id=user_id).exclude(from_user_id__in=blocked_users + blocked_by_users).count()

            # Check if token user is following query user (only if they're different)
            is_following = False
            if token_user_id != user_id and query_user_id is not None:
                is_following = Follow.objects.filter(
                    from_user_id=token_user_id, 
                    to_user_id=query_user_id
                ).only('id').exists()

            # Check block status (only if they're different)
            is_blocked = False
            if token_user_id != user_id and query_user_id is not None:
                is_blocked = Block.objects.filter(
                    from_user_id=token_user_id,
                    to_user_id=query_user_id
                ).only('id').exists()
                if is_blocked:
                    followers_count = 0
                    following_count = 0

            # Get user posts efficiently (only fetch fields needed by serializer)
            user_posts = Post.objects.filter(
                user_id=user_id, 
                is_deleted=False,
                is_posted=True
            ).order_by('-created_at')

            post_serializer = PostViewAdminSerializer(
                user_posts, 
                many=True, 
                context={'request': request}
            )
            posts = []

            # Get brands efficiently with .only()
            brands = Brands.objects.filter(user_id=user_id).only('id', 'name', 'logo', 'email')
            profile_brands = [
                {
                    'id': brand.pk,
                    'name': brand.name,
                    'logo': f"{header}{domain}{brand.logo.url}" if brand.logo else '',
                    'email': brand.email,
                }
                for brand in brands
            ]

            data = {
                'id': user_data.pk,
                'name': user_data.name,
                'username': user_data.username,
                'email': user_data.enc_email,
                'bio': user_data.bio or '',
                'dob': user_data.dob or '',
                'mobile': user_data.phone,
                'country': user_data.country,
                'state': user_data.state,
                'city': user_data.city,
                'gender': user_data.gender or '',
                'profile_image': profile_image,
                'is_blocked': is_blocked,
                'number_of_post': user_posts.count(),
                'number_of_followers': followers_count,
                'number_of_following': following_count,
                'is_following': is_following,
                'profile_brands': profile_brands,
                'refference_code': user_data.refference_code,
                'posts': posts
            }

            return Response(
                {'status': True, 'message': 'User Profile Found Successfully', 'data': data}, 
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class EditUserProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            name = request.data.get('name')
            username = request.data.get('username')
            bio = request.data.get('bio')
            mobile = request.data.get('mobile')
            dob = request.data.get('dob')
            gender = request.data.get('gender')
            profile = request.FILES.get('profile_image')
            country = request.data.get('country')
            state = request.data.get('state')
            city = request.data.get('city')
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            try:
                user_data = UserRegistration.objects.get(pk=user_id)
                if name:
                    user_data.name = name
                if username:
                    user_data.username = username
                if bio:
                    user_data.bio = bio
                if mobile:
                    user_data.verify_phone = custom_hasher(mobile)
                    user_data.phone = mobile
                if dob:
                    user_data.dob = dob
                if gender:
                    user_data.gender = gender
                if profile:
                    user_data.profile_picture = profile
                if country:
                    user_data.country = country
                if state:
                    user_data.state = state
                if city:
                    user_data.city = city
                user_data.save()
                mongo_profile_picture = user_data.profile_picture.url if user_data.profile_picture else ''
                db_update_user_data(user_id,user_data.username,user_data.name,mongo_profile_picture)
                return Response({'status': True, 'message': 'User Profile Updated Successfully', 'image': f'{header}{domain}{user_data.profile_picture.url}' if user_data.profile_picture else ''}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeleteAccountView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(pk=user_id)
            user.is_deleted = True
            user.save()
            return Response({'status': True, 'message': 'Account Deleted Successfully'}, status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeleteAccountEmailView(APIView):
    @sentry_sdk.trace

    def post(self, request):
        try:
            try:
                email = request.data.get('email')
                print(email)
                user = UserRegistration.objects.get(enc_email=email)
                print(user)
                if user.is_deleted == True:
                    return Response({'status': False, 'message': 'Account Already Deleted'}, status=status.HTTP_400_BAD_REQUEST)
                user.is_deleted = True
                user.save()
                return Response({'status': True, 'message': 'Account Deleted Successfully'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'Account Not Found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class InstagramCallbackView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        code = request.GET.get('code')
        brand_id = request.GET.get('state')
        long_live_token = ''
        access_token = ''
        user_id = ''
        if not code:
            return Response({"error": "Authorization code not provided"}, status=400)
        request_url = "https://api.instagram.com/oauth/access_token/"
        data = {
            'client_id': '****************',
            'client_secret': 'a6d4e0f44aa2b92f43d8dc379a6316fd',
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': 'https://api.flowkar.com/api/instagram/'
        }
        post_request = requests.post(request_url, data=data)
        if post_request.status_code == 200 or post_request.status_code == 201:
            responce_data = post_request.json()
            access_token = responce_data['access_token']
            user_id = responce_data['user_id']
            long_token_url = 'https://graph.instagram.com/access_token'
            params = {
                "grant_type": "ig_exchange_token",
                "client_secret": 'a6d4e0f44aa2b92f43d8dc379a6316fd',
                "access_token": access_token
            }
            get_request = requests.get(long_token_url, params=params)
            if get_request.status_code == 200:
                get_responce = get_request.json()
                long_live_token = get_responce['access_token']
            if brand_id != 'None':
                user_obj = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_obj.instagram_check = True
                user_obj.insta_auth_token = long_live_token if long_live_token !=  '' else access_token
                user_obj.insta_user_id = user_id
                user_obj.save()
                is_connected_once = db_get_user_social(brand_id,"instagram")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_obj.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,instagram=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        return JsonResponse({'status': True, 'message': 'User Authenticated Successfully', 'token': long_live_token if long_live_token !=  '' else access_token, 'user_id': user_id})


class FacebookAuthUrl(APIView):
    @sentry_sdk.trace

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user = decode_token(auth_token)
        brand_id = request.headers.get('brand')
        url = generate_facebook_auth_url_dev(brand_id)
        return Response({'status': True, 'message': 'Facebook Url Generated', 'url': url}, status=status.HTTP_200_OK)


class FaceboookCallbackView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        try:
            code = request.GET.get('code')
            brand_id = request.GET.get('state')
            if not code:
                return Response({"error": "Authorization code not provided"}, status=400)
            long_lived_token, page_id = facebook_code_to_token(code)
            if page_id == 0:
                return JsonResponse({'error':'Authentication Failed - Did Not Match The Facebook Business Requirements'},safe=False)
            if brand_id != 'None':
                user_obj = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_obj.facebook_check = True
                user_obj.facebook_token = long_lived_token
                user_obj.facebook_page_id = page_id
                user_obj.save()
                is_connected_once = db_get_user_social(brand_id,"facebook")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_obj.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,facebook=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except (Exception , IndexError):
            return JsonResponse({'error':'Authentication Failed - Did Not Match The Facebook Business Requirements'},safe=False)



class InstagramAfterAuth(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            token = request.data.get('token')
            retrived_user_id = request.data.get('user_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.instagram_check = True
                user_data.insta_auth_token = token
                user_data.insta_user_id = retrived_user_id
                user_data.save()
                return Response({'status': True, 'message': 'User Instagram Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    instagram_check=True,
                    insta_auth_token=token,
                    insta_user_id=retrived_user_id
                )
                return Response({'status': True, 'message': 'User Instagram Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class FacebookAfterAuth(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            token = request.data.get('token')
            page_id = request.data.get('page_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.facebook_check = True
                user_data.facebook_token = token
                user_data.facebook_page_id = page_id
                user_data.save()
                return Response({'status': True, 'message': 'User Facebook Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    facebook_check=True,
                    facebook_token=token,
                    facebook_page_id=page_id
                )
                return Response({'status': True, 'message': 'User Facebook Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


# TikTok

class TikTokUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = brand_id
        url = tiktok_auth_url(user_id)
        return Response({'status': True, 'message': 'TikTok Url Generated', 'url': url}, status=status.HTTP_200_OK)


class TikTokAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        code = request.GET.get('code')
        brand_id = request.GET.get('state')
        token = decode_url(code)
        access_token, refresh_token = tiktok_code_token(code)
        try:
                user_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                user_data.tiktok_check = True
                user_data.tiktok_access_token = access_token
                user_data.tiktok_refresh_token = refresh_token
                user_data.save()
                is_connected_once = db_get_user_social(brand_id,"tiktok")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,tiktok=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    brand_id=brand_id,
                    tiktok_check=True,
                    tiktok_access_token=access_token,
                    tiktok_refresh_token=refresh_token
                )
                is_connected_once = db_get_user_social(brand_id,"tiktok")
                if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_data.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,tiktok=True)

                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')


class TikTokAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            access_token = request.data.get('access_token')
            refresh_token = request.data.get('refresh_token')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.tiktok_check = True
                user_data.tiktok_access_token = access_token
                user_data.tiktok_refresh_token = refresh_token
                user_data.save()
                return Response({'status': True, 'message': 'User TikTok Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    tiktok_check=True,
                    tiktok_access_token=access_token,
                    tiktok_refresh_token=refresh_token
                )
                return Response({'status': True, 'message': 'User TikTok Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

# Threads


class ThreadUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = brand_id
        url = generate_threads_url(user_id)
        return Response({'status': True, 'message': 'Thread Url Generated', 'url': url}, status=status.HTTP_200_OK)


class ThreadAuthView(APIView):
    @sentry_sdk.trace

    def get(self, request):
        code = request.GET.get('code')
        access_token, user_id = threads_after_auth(code)
        brand_id = request.GET.get('state')
        if brand_id != 'None':
            user_obj = ThirdPartyAuth.objects.get(brand_id=brand_id)
            user_obj.thread_check = True
            user_obj.thread_auth_token = access_token
            user_obj.thread_user_id = user_id
            user_obj.save()
            is_connected_once = db_get_user_social(brand_id,"threads")
            if is_connected_once == False:
                    referal_points = db_get_points("social_connect")
                    db_update_points(user_obj.brand.user.pk,referal_points[0],"Social Connect Reward Credited")
                    db_update_user_social(brand_id,threads=True)

            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        return JsonResponse({'status': True, 'message': 'User Authenticated Successfully', 'access_token': access_token, 'user_id': user_id, 'unique_id': ''})


class ThreadAfterAuthView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            access_token = request.data.get('access_token')
            thread_user_id = request.data.get('user_id')
            try:
                user_data = ThirdPartyAuth.objects.get(user_id=user_id)
                user_data.thread_check = True
                user_data.thread_auth_token = access_token
                user_data.thread_user_id = thread_user_id
                user_data.save()
                return Response({'status': True, 'message': 'User Thread Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                create = ThirdPartyAuth.objects.create(
                    user_id=user_id,
                    thread_check=True,
                    thread_auth_token=access_token,
                    thread_user_id=thread_user_id
                )
                return Response({'status': True, 'message': 'User Thread Authenticated Successfully', 'Connect': True}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


# Y TO FLOWKAR

class GetUserProfileAdminView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            current_date = datetime.now().strftime('%Y-%m-%d')
            print(current_date)
            try:
                user_data = UserRegistration.objects.get(pk=user_id)
                try:
                    domain = request.get_host()
                    profile_picture = user_data.profile_picture.url
                    if '127.0.0.1' in domain:
                        header = 'https://'
                    else:
                        header = 'https://'
                except ValueError:
                    profile_picture = ''
                    header = ''
                    domain = ''
                try:
                    video_formats = [
                        'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
                        'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
                        'ts', 'ogv', 'rm', 'rmvb'
                    ]
                    post_data = Post.objects.filter(user_id=user_id).exclude(
                        is_deleted=True).exclude(is_posted=False).order_by('-created_at')
                    scheduled_posts_count = Post.objects.filter(user_id=user_id, is_scheduled=True, is_deleted=False).count()

                    post_serializer = PostViewAdminSerializer(
                        post_data[:10], many=True, context={'request': request})
                    posts = post_serializer.data
                    date_range = get_week_dates_str(str(current_date))
                    platforms_dict = {}
                    week_range = Post.objects.filter(
                        Q(user_id=user_id), Q(created_at__date__range=(date_range[0], date_range[-1]))).exclude(is_deleted=True).exclude(is_posted=False)
                    for platform in week_range:
                        if platform.facebook:
                            platforms_dict['Facebook'] = True
                        if platform.instagram:
                            platforms_dict['Instagram'] = True
                        if platform.linkedin:
                            platforms_dict['Linkedin'] = True
                        if platform.pinterest:
                            platforms_dict['Pinterest'] = True
                        if platform.vimeo:
                            platforms_dict['Vimeo'] = True
                        if platform.youtube:
                            platforms_dict['Youtube'] = True
                        if platform.dailymotion:
                            platforms_dict['Dailymotion'] = True
                        if platform.twitter:
                            platforms_dict['Twitter'] = True
                        if platform.tumblr:
                            platforms_dict['tumblr'] = True
                        if platform.reddit:
                            platforms_dict['reddit'] = True

                    video_url = []
                    videos = PostFiles.objects.filter(
                        Q(post_id__in=week_range), Q(post__is_deleted=False))
                    for data in videos:
                        if data.file.path.split(".")[-1].lower() in video_formats:
                            video_url.append(data.file)
                    number_of_post = week_range.count()
                    number_of_video = len(video_url)

                except Post.DoesNotExist:
                    posts = ''
                    number_of_post = ''
                    number_of_video = ''
                    platforms_dict = ''

                try:
                    followers = Follow.objects.filter(
                        to_user_id=user_id).count()
                    following = Follow.objects.filter(
                        from_user_id=user_id).count()
                except Follow.DoesNotExist:
                    followers = 0
                    following = 0
                data = {
                    'id': user_data.pk,
                    'name': user_data.name,
                    'username': user_data.username,
                    'email': user_data.enc_email,
                    'bio': user_data.bio if user_data.bio != '' else '',
                    'dob': user_data.dob if user_data.dob != '' else '',
                    'mobile': user_data.phone,
                    'gender': user_data.gender if user_data.gender != '' else '',
                    'profile_image': f"{header}{domain}{profile_picture}",
                    'total_post_count': post_data.count(),
                    'scheduled_count': scheduled_posts_count,
                    'number_of_followers': followers,
                    'number_of_following': following,
                    'total_week_post': number_of_post,
                    'total_week_video': number_of_video,
                    'platforms': platforms_dict,
                    'posts': posts,
                    'country':user_data.country,
                    'state':user_data.state,
                    'city':user_data.city,
                }
                return Response({'status': True, 'message': 'User Profile Found Successfully', 'data': data}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetUserPostsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = UserProfilePagination
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            query_user_id = request.query_params.get('user_id')
            token_user_id = decode_token(auth_token)
            paginator = self.pagination_class()
            
            try:
                domain = request.get_host()
                header = 'https://'  # Simplified this logic
            except ValueError:
                header = ''
                domain = ''
                
            user_id = query_user_id if query_user_id else token_user_id
            
            try:
                # Check if user exists in one query
                if not UserRegistration.objects.filter(pk=user_id).exists():
                    return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({'status': False, 'message': f'Error validating user: {e}'}, status=status.HTTP_400_BAD_REQUEST)
                
            request_platform = request.query_params.get('request_platform')
            
            if request_platform == '1' or request_platform is None:
                # Main query with annotations to reduce subsequent queries
                post_queryset = Post.objects.filter(
                    user_id=user_id, 
                    is_deleted=False, 
                    is_posted=True,
                    is_text_post=False
                ).select_related(
                    'user'  # Get user data in the same query
                ).prefetch_related(
                    'postfiles_set',  # Prefetch all post files
                    'postfiles_set__postfilesthumbnail_set',  # Prefetch thumbnails
                    Prefetch(
                        'comment_set',  # Assuming related_name is 'comment_set'
                        queryset=Comment.objects.select_related('user').order_by('-pk'),
                        to_attr='prefetched_comments'
                    )
                ).annotate(
                    likes_count=Count('likepost'),  # Count likes in the database
                    is_liked=Exists(
                        LikePost.objects.filter(
                            post_id=OuterRef('pk'),
                            user_id=token_user_id
                        )
                    ),
                    is_saved=Exists(
                        SavedPost.objects.filter(
                            post_id=OuterRef('pk'),
                            user_id=token_user_id
                        )
                    )
                ).order_by('-created_at')
                
                all_data = []
                for post in post_queryset:
                    # Process files only once per post
                    files = [f'{header}{domain}{file.file.url}' for file in post.postfiles_set.all()]
                    width, height = get_image_dimensions(files[0]) if files else (0, 0)
                    
                    # Process thumbnails
                    thumbnail_files = [
                        f'{header}{domain}{thumbnail.file.url}'
                        for file in post.postfiles_set.filter(is_video=True)
                        for thumbnail in file.postfilesthumbnail_set.all()
                    ]
                    
                    # Get latest comment if available
                    latest_comment = ''
                    if hasattr(post, 'prefetched_comments') and post.prefetched_comments:
                        comment = post.prefetched_comments[0]
                        latest_comment = f'{comment.user.username} {comment.comment_text}'
                    
                    append_object = {
                        'id': post.pk,
                        'title': post.title,
                        'description': post.description,
                        'location': post.location,
                        'likes': post.likes_count,
                        'dislikes': post.dislikes,
                        'comments_count': post.comments_count,
                        'tagged_in': post.tagged_in,
                        'created_at': post.created_at,
                        'scheduled_at': post.scheduled_at if post.scheduled_at else '',
                        'files': files,
                        'width': width,
                        'height': height,
                        'thumbail_files': thumbnail_files,
                        'latest_comment': latest_comment,
                        'user': {
                            'user_id': post.user.pk,
                            'username': post.user.username,
                            'name': post.user.name,
                            'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                        },
                        'is_liked': post.is_liked,
                        'is_saved': post.is_saved,
                        'is_text_post': post.is_text_post
                    }
                    all_data.append(append_object)
                
                result_page = paginator.paginate_queryset(all_data, request)
                return paginator.get_paginated_response({
                    'status': True, 
                    'message': 'Data Found Successfully', 
                    'data': result_page
                })
                
            elif request_platform == '2':
                # For admin view, optimize the query and use serializer
                data = Post.objects.filter(
                    user_id=user_id,
                    is_deleted=False,
                    is_posted=True
                ).select_related('user').order_by('-created_at')
                
                result_page = paginator.paginate_queryset(data, request)
                post_serializer = PostViewAdminSerializer(
                    result_page, many=True, context={'request': request})

                return paginator.get_paginated_response({
                    'status': True, 
                    'message': 'Data Found Successfully', 
                    'data': post_serializer.data
                })
                
        except Exception as e:
            return Response({'status': False, 'message': f'Error: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        

class BlockedUsersListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            brand_id = request.headers.get('brand')
            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)

            if 6 not in role_permission_result :
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)
            
            blocked_users = Block.objects.filter(Q(from_user_id=user_id)).values_list('to_user_id', flat=True)
            blocked_users_data = UserRegistration.objects.filter(
                id__in=blocked_users)

            data = []
            for user in blocked_users_data:
                data.append({
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'is_blocked': True,
                    'profile_picture': user.profile_picture.url if user.profile_picture else '',
                })

            return Response({'status': True, 'message': 'Blocked users retrieved successfully.', 'data': data}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class BlockUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            brand_id = request.headers.get('brand')
            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)

            if 6 not in role_permission_result :
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)
            
            to_user_id = request.data.get('to_user_id')

            if not to_user_id:
                return Response({'status': False, 'message': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                to_user = UserRegistration.objects.get(id=to_user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)

            if to_user_id == user_id:
                return Response({'status': False, 'message': 'Users cannot block/unblock themselves'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                block = Block.objects.get(
                    from_user_id=user_id, to_user=to_user)

                if block:
                    block.delete()
                    return Response({'status': True, 'message': 'User unblocked successfully', 'is_blocked': False}, status=status.HTTP_200_OK)
            except Block.DoesNotExist:
                Block.objects.create(from_user_id=user_id, to_user=to_user)
                return Response({'status': True, 'message': 'User blocked successfully', 'is_blocked': True}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GraphApi(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def get(self, request):
        user_from_date = request.query_params.get('from_date')
        user_to_date = request.query_params.get('to_date')
        from_date = datetime.strptime(user_from_date, '%Y-%m-%d')
        to_date = datetime.strptime(user_to_date, '%Y-%m-%d')
        date_list = [from_date + timedelta(days=x)
                     for x in range((to_date - from_date).days + 1)]

        data_list = []

        i = 0
        for date in date_list:
            i += 1
            user_data = UserRegistration.objects.filter(
                created_at__year=date.year,
                created_at__month=date.month,
                created_at__day=date.day
            ).aggregate(count=Count('id'))
            data_list.append({
                'id': i,
                'day': date.strftime('%A'),
                'date': date.strftime('%d-%m-%Y'),
                'count': user_data['count']
            })

        return Response({'status': True, 'message': 'Data Found Successfully', 'data': data_list})
    
# P1.2

class RegisterBrand(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        # try:
            auth_token = request.headers.get('Authorization')
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)
            check_sub = check_user_plan(user_id,int(subscription_id))
            if check_sub['limits']['brands_left'] == False:
                return Response({'status': False,'message': 'Please subscribe to a  Premuim plan'}, status=status.HTTP_200_OK)
            name = request.data.get('name')
            email = request.data.get('email')
            domain = request.data.get('domain')
            logo_file = request.FILES.get('logo_file')
            if not name or not email or not logo_file:
                return Response({'status': False,'message': 'Please submit the required fields'}, status=status.HTTP_400_BAD_REQUEST)
            
            brand = Brands.objects.create(
                user_id=user_id,
                name=name,
                email=email,
                domain=domain,
                logo=logo_file
            )
            db_create_user_social(brand.pk)
            create_third_party = ThirdPartyAuth.objects.create(
                brand=brand
            )
            return Response({'status': True,'message': 'Brand Registered Successfully','brand_id':brand.pk}, status=status.HTTP_200_OK)
        # except Exception as e:
            # return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class EditBrandView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.data.get('brand_id')
            name = request.data.get('name')
            email = request.data.get('email')
            domain = request.data.get('domain')
            logo_file = request.FILES.get('logo_file')
            if not name or not email or not logo_file:
                return Response({'status': False,'message': 'Please submit the required fields'}, status=status.HTTP_400_BAD_REQUEST)
            
            brand = Brands.objects.get(pk=brand_id)
            brand.name = name
            brand.email = email
            brand.domain = domain
            brand.logo = logo_file
            brand.save()
            user = UserRegistration.objects.get(pk=user_id)
            # user.current_status='4'
            return Response({'status': True,'message': 'Brand Updated Successfully','user_status': user.current_status}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class EditBrandWebView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            brand_id = request.data.get('brand_id')
            if not brand_id:
                return Response({'status': False, 'message': 'Brand ID is required'}, status=status.HTTP_400_BAD_REQUEST)

            brand = Brands.objects.get(pk=brand_id, user_id=user_id)

            name = request.data.get('name')
            email = request.data.get('email')
            domain = request.data.get('domain')
            logo_file = request.FILES.get('logo_file')

            if name:
                brand.name = name
            if email:
                brand.email = email
            if domain:
                brand.domain = domain
            if logo_file:
                brand.logo = logo_file

            brand.save()

            user = UserRegistration.objects.get(pk=user_id)

            return Response({
                'status': True,
                'message': 'Brand updated successfully',
                'user_status': user.current_status
            }, status=status.HTTP_200_OK)

        except Brands.DoesNotExist:
            return Response({'status': False, 'message': 'Brand not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class DeleteBrandView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.data.get('brand_id')

            if not brand_id:
                return Response({'status': False, 'message': 'Brand ID is required.'}, status=status.HTTP_400_BAD_REQUEST)
            
            brand_count = Brands.objects.filter(user_id=user_id).count()

            if brand_count <= 1:
                return Response({'status': False, 'message': 'At least one brand is required and cannot be deleted. We appreciate your understanding.'}, status=status.HTTP_400_BAD_REQUEST)

            brand = Brands.objects.get(pk=brand_id)
            brand.delete()
            return Response({'status': True, 'message': 'Brand deleted successfully.'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetBrandDetails(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)

            brands = Brands.objects.filter(Q(user_list__contains=user_id) | Q(user_id=user_id))
            user = UserRegistration.objects.get(pk=user_id)
            
            data = []

            if brand_id:
                brands = Brands.objects.get(pk=brand_id, user_id=user_id)
                append_response = {
                    'id':brands.pk,
                    'name':brands.name,
                    'email':brands.email,
                    'domain':brands.domain,
                    'is_invited' : False if brands.user.pk == user_id else True ,
                    'logo':brands.logo.url if brands.logo else ''
                }
                data.append(append_response)
                return Response({'status': True,'message': 'Brand Details Found','data':data}, status=status.HTTP_200_OK)
            
            for brand in brands:
                if brand.user.pk != user_id and brand.name == brand.user.name:
                    append_response = {
                        'id':brand.pk,
                        'name':brand.name,
                        'email':brand.email,
                        'domain':brand.domain,
                        'is_invited' : False if brand.user.pk == user_id else True ,
                        'logo':brand.logo.url if brand.logo else ''
                    }
                    data.append(append_response)
                else:
                    if brand.name == user.name:
                        append_response = {
                            'id':brand.pk,
                            'name':brand.name,
                            'email':brand.email,
                            'domain':brand.domain,
                            'is_invited' : False if brand.user.pk == user_id else True ,
                            'logo':brand.logo.url if brand.logo else ''
                        }
                        data.append(append_response)
            return Response({'status': True,'message': 'Brand Details Found','data':data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class SelectBrandView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.data.get('brand_id')
            try:
                brand = Brands.objects.get(pk=brand_id)
                get_third_party = ThirdPartyAuth.objects.get(brand=brand)
                data = {
                    'brand_id':brand.pk,
                    'facebook':get_third_party.facebook_check,
                    'instagram':get_third_party.instagram_check,
                    'tiktok':get_third_party.tiktok_check,
                    'thread':get_third_party.thread_check,
                    'linkedin':get_third_party.linkedin_check,
                    'pinterest':get_third_party.pinterest_check,
                    'vimeo':get_third_party.vimeo_check,
                    'youtube':get_third_party.youtube_check,
                    'tumblr':get_third_party.tumblr_check,
                    'reddit':get_third_party.reddit_check,
                }
                return Response({'status': True,'message': 'Brand Selected Successfully','data':data}, status=status.HTTP_200_OK)
            except Brands.DoesNotExist:
                return Response({'status': False,'message': 'Brand Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class HomeScreenApi(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(pk=user_id)
            brands = Brands.objects.filter(user_id=user_id)
            for brand in brands:
                db_create_user_social(brand.pk)
            user_dict = model_to_dict(user)
            if 'id' in user_dict:
                user_dict['_id'] = user_dict.pop('id')
            for field_name, value in user_dict.items():
                if isinstance(value, FieldFile):
                    if value and value.name:
                        user_dict[field_name] = value.url
                    else:
                        user_dict[field_name] = ''

            db_register_user(user_dict)
            db_create_user_points(user_id)
            db_create_or_update_login_activity(user_id,datetime.now())
            login_data = db_get_user_login_activity(user_id)
            is_login_today = has_logged_in_today(login_data['log_data'])
            is_login_7d = has_seven_day_streak(login_data['log_data'])
            if is_login_today:
                daily_login_points = db_get_points('daily_login')
                db_update_points(user_id,daily_login_points[0],"Daily Login Reward Credited")
                
            if is_login_7d:
                if login_data['seven_day_active'] == False:
                    daily_login_points = db_get_points('seven_day_active')
                    db_update_user_activity_status(user_id)

            profile_completion_status, persantage = get_profile_completion_status(user_id)

            if profile_completion_status == True:
                if login_data['seven_day_active'] == False:
                    profile_completion_point = db_get_points('completing_profile')
                    is_points_credited = db_get_user_profile_status(user_id)
                    print("is_points_credited",is_points_credited)
                    if is_points_credited == False:
                        db_update_points(user_id,profile_completion_point[0],"Profile Completion Reward Credited")
                        db_update_user_profile_status(user_id)
                    else:
                        pass
            
            try:
                refferal_user = Reffrence.objects.get(invited_id=user_id)
                if refferal_user.is_point_given == False:
                    refference_points = db_get_points('invite_user')
                    db_update_points(user_id,refference_points[1],"Referral Reward Credited")
                    db_update_points(refferal_user.invitee.pk,refference_points[0],"Referral Reward Credited")
                    refferal_user.is_point_given = True
                    refferal_user.save()

                if refferal_user.is_first_activity == False:
                    refference_points = db_get_points('invite_user_premium')
                    db_update_points(refferal_user.invitee.pk,refference_points[0],"Referral Reward Credited")
                    refferal_user.is_first_activity = True
                    refferal_user.save()
                    
            except Reffrence.DoesNotExist:
                pass

            is_opt_in_status = SurveyForm.objects.get(Q(user_id=user_id))
            subscription = UserSubscription.objects.get(Q(user_id=user_id))
            sub_status = is_opt_in_status.is_opt_in
            user_status = user.current_status
            is_beta_tester = False
            try:
                beta_tester = BetaTester.objects.filter(email=user.enc_email).first()
                if beta_tester:
                    is_beta_tester = True
            except BetaTester.DoesNotExist:
                is_beta_tester = False
            
            if subscription.subscription.pk != premium_subscription_id:
                subscription.subscription_id = premium_subscription_id
                subscription.start_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
                subscription.end_date = (datetime.now() + timedelta(days=1000)).strftime("%Y-%m-%d %H:%M:%S.%f")
                subscription.save()
                
            if user.current_status == '3' and  subscription.subscription.pk == premium_subscription_id:
                start_date_obj = datetime.strptime(subscription.start_date, "%Y-%m-%d %H:%M:%S.%f")
                if datetime.now() + timedelta(days=3) > start_date_obj:
                    user.current_status = '5'
                    user.save()
                    user_status = '5'
            user_id = user.pk
            subscription_id = subscription.subscription.pk
            print(user_status)
            reward_points_lv_1 = db_get_points("800_points")
            reward_points_lv_2 = db_get_points("1200_points")
            user_points = db_get_user_points(user_id)
            user_point_data = db_get_user_points_data(user_id)
            if user_points >= 800:
                if user_point_data['level_1'] == False:
                    db_update_points(user_id,reward_points_lv_1[1],"Level 1 Reward Credited")
                    db_update_level(user_id,1)

                if user_points >= 1200:
                    if user_point_data['level_2'] == False:
                        db_update_points(user_id,reward_points_lv_2[1],"Level 2 Reward Credited")
                        db_update_level(user_id,2)

            try:
                check_user = SpecialSocial.objects.get(user_id=user_id,is_active=True)
                if check_user.created_at < timezone.now() - timedelta(days=30):
                    check_user.is_active = False
                    check_user.created_at = None
                    check_user.save()
            except SpecialSocial.DoesNotExist:
                pass
            return Response({'status': True,'message': 'User Data','user_type':user.user_type,'user_id':user_id,'username':user.username,'email':user.enc_email,'is_beta_tester':is_beta_tester,'subscription_id':subscription_id,'is_opt_in':True,'user_status':user_status}, status=status.HTTP_200_OK)
        except Exception as e: 
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class SurveyForms(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            name = request.data.get('name')
            is_app_used = request.data.get('is_app_used')
            app_description = request.data.get('app_description')
            app_name = request.data.get('app_name')
            is_opt_in = request.data.get('is_opt_in')
            start_date = datetime.now()
            
            if is_opt_in == True:
                user = UserRegistration.objects.get(pk=user_id)
                user.is_subsribed = True
                user.save()
                try:
                    subscription = Subscriptions.objects.get(pk=premium_subscription_id)
                    end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))
                    user_subs = UserSubscription.objects.get(Q(user_id=user_id))
                    user_subs.subscription_id = premium_subscription_id
                    user_subs.start_date = start_date
                    user_subs.end_date = end_date
                    user_subs.save()

                except UserSubscription.DoesNotExist: 
                    subscription = Subscriptions.objects.get(pk=premium_subscription_id)
                    end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))

                    UserSubscription.objects.create(
                        user_id = user_id,
                        start_date = start_date,
                        end_date = end_date,
                        subscription_id = premium_subscription_id,
                    )
            else:
                try:
                    subscription = Subscriptions.objects.get(pk=free_subscription_id)
                    end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))
                    user_subs = UserSubscription.objects.get(Q(user_id=user_id))
                    user_subs.subscription_id = free_subscription_id
                    user_subs.start_date
                    user_subs.end_date = end_date
                    user_subs.save()

                except UserSubscription.DoesNotExist: 
                    subscription = Subscriptions.objects.get(pk=free_subscription_id)
                    end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))
                    UserSubscription.objects.create(
                        user_id = user_id,
                        start_date = start_date,
                        end_date = end_date,
                        subscription_id = free_subscription_id,
                    )
            try:
                existing_survey = SurveyForm.objects.get(Q(user_id=user_id))
                existing_survey.name = name
                existing_survey.is_app_used = is_app_used
                existing_survey.app_description = app_description
                existing_survey.app_name = app_name
                existing_survey.is_opt_in = is_opt_in
                user = UserRegistration.objects.get(pk=user_id)
                user.current_status = '5'
                user.save()
                existing_survey.save()
                return Response({'status': True,'message': 'Survey form updated successfully','is_opt_in':is_opt_in,'user_status':user.current_status}, status=status.HTTP_200_OK)
            except SurveyForm.DoesNotExist:
                create = SurveyForm.objects.create(
                    user_id=user_id,
                    name=name,
                    is_app_used=is_app_used,
                    app_description=app_description,
                    app_name=app_name,
                    is_opt_in=is_opt_in,
                )
                user = UserRegistration.objects.get(pk=user_id)
                user.current_status = '5'
                user.save()
                return Response({'status': True,'message': 'Survey form submitted successfully','is_opt_in':is_opt_in,'user_status':user.current_status}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class UpdateSubscriptionStatusView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            is_opt_in = request.data.get('is_opt_in')
            user = SurveyForm.objects.get(user_id=user_id)
            if is_opt_in == True:
                user_subscription = UserSubscription.objects.get(Q(user_id=user_id))
                subscription = Subscriptions.objects.get(pk=premium_subscription_id)
                start_date = datetime.now()
                end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))
                user_subscription.subscription_id = premium_subscription_id
                user_subscription.start_date = start_date
                user_subscription.end_date = end_date
                user.is_opt_in = True
                user_subscription.save()
                user.save()
            else:
                user.is_opt_in = False
                user.save()
            return Response({'status': True,'message': 'Subscription status Updated', 'is_opt_in': user.is_opt_in}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


#SUBSCRIPTIONS 

class CreateSubscriptionView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        try:
            subscription = request.headers.get('subscription')
            subscriptions = Subscriptions.objects.all()
            data = []
            for sub_data in subscriptions:
                append_object = {
                    'id' : sub_data.pk,
                    'subscription_name' :sub_data.subscription_name,
                    'subscription_price' :sub_data.subscription_price,
                    'subscription_duration' : sub_data.subscription_duration,
                    'subscription_description' : sub_data.subscription_description,
                    'is_live_status' :sub_data.is_live_status
                }
                data.append(append_object)
            return Response({'status': True,'message': 'Subscription List', 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

    @sentry_sdk.trace
    def post(self,request):
        try:
            subscription_name = request.data.get('subscription_name')
            subscription_price = request.data.get('subscription_price')
            subscription_duration = request.data.get('subscription_duration')
            subscription_description = request.data.get('subscription_description')
            subscription = Subscriptions.objects.create(
                subscription_name=subscription_name,
                subscription_price=subscription_price,
                subscription_duration=subscription_duration,
                subscription_description=subscription_description
            )
            return Response({'status': True,'message': 'Subscription Created Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class UserSubscribeView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace

    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            subscription_id = request.data.get('subscription_id')
            user = UserRegistration.objects.get(pk=user_id)
            subscription = Subscriptions.objects.get(pk=subscription_id)
            start_date = datetime.now()
            end_date = datetime.now() + timedelta(days=int(subscription.subscription_duration))
            print(start_date)
            print(end_date)
            try:
                check_subscribed = UserSubscription.objects.get(Q(user_id=user_id), Q(is_active=True))
                return Response({'status': False,'message': 'User already subscribed','subscription_name':check_subscribed.subscription.subscription_name}, status=status.HTTP_400_BAD_REQUEST)
            except UserSubscription.DoesNotExist:
                user_subscription = UserSubscription.objects.create(
                    user_id=user_id,
                    subscription_id=subscription_id,
                    start_date=str(start_date),
                    end_date=str(end_date),
                    is_active=True
                )
                user.is_subsribed = True
                user.save()
            return Response({'status': True,'message': 'User Subscribed Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class CheckUserStatusView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(pk=user_id)
            brand_id = ''
            try:
                current_brand = CurrentBrand.objects.get(user_id=user_id)
                third_party_data = ThirdPartyAuth.objects.get(brand_id=current_brand.brand.pk)
                brand_id=current_brand.brand.pk

            except CurrentBrand.DoesNotExist:
                current_brand = Brands.objects.filter(user_id=user_id).first()
                third_party_data = ThirdPartyAuth.objects.get(brand_id=current_brand.pk)
                brand_id = current_brand.pk

            print(f'Current {current_brand}')
            user_status = user.current_status
            facebook = third_party_data.facebook_check
            instagram = third_party_data.instagram_check
            linkedin = third_party_data.linkedin_check
            pinterest = third_party_data.pinterest_check
            vimeo = third_party_data.vimeo_check
            tumblr = third_party_data.tumblr_check
            reddit = third_party_data.reddit_check
            tiktok = third_party_data.tiktok_check
            threads = third_party_data.thread_check
            youtube = third_party_data.youtube_check
            dailymotion = False
            X = False
            check_list = [facebook, instagram, linkedin,
                                  pinterest, vimeo, youtube, dailymotion, X,tumblr,reddit,tiktok,threads]
            is_any_auth = False
            for i in check_list:
                if i == True:
                    is_any_auth = True
                else:
                    pass

            return Response({'status': True,'message': 'User Status Found','user_status':user_status,'brand_id':brand_id,"username":user.username,"is_any_auth":is_any_auth}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetPendingInviteUsersView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            pending_users = UserManagement.objects.filter(user_id=user_id, is_accepted=False)

            data = []
            for user in pending_users:
                user_data = UserRegistration.objects.get(pk=user.user_invited_id)
                data.append({
                    'id': user_data.pk,
                    'name': user_data.name,
                    'username': user_data.username,
                    'email': user_data.enc_email,
                    'profile_image': user_data.profile_picture.url if user_data.profile_picture else '',
                })
            return Response({'status': True, 'message': 'Pending Invited Users Found', 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ApproveInviteUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            invited_user_id = request.data.get('invited_user_id')
            try:
                user_mgmt = UserManagement.objects.get(user_id=user_id, user_invited_id=invited_user_id, is_accepted=False)
            except UserManagement.DoesNotExist:
                return Response({'status': False, 'message': 'No such pending invitation found'}, status=status.HTTP_400_BAD_REQUEST)

            user_mgmt.is_accepted = True
            user_mgmt.save()

            brand_links = UserBrandAccessManagement.objects.filter(user_management=user_mgmt)
            for brand_link in brand_links:
                brand = brand_link.brand
                if invited_user_id not in brand.user_list:
                    brand.user_list.append(invited_user_id)
                    brand.save()
            return Response({'status': True, 'message': 'User approved Successfully.'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class GetIndustryListView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        try:
            industries = Industry.objects.all()
            user_types = UserTypes.objects.all()
            industry_data = []
            user_type_data = []
            for industry in industries:
                industry_data.append({
                    'id':industry.pk,
                    'industry_name':industry.name
                })

            for user_type in user_types:
                user_type_data.append({
                    'id':user_type.pk,
                    'type_name':user_type.name
                })
            data = {
                'industries':industry_data,
                'user_types':user_type_data
            }
            return Response({'status': True,'message': 'Industry List Found','data':data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class CreateIndustryView(APIView):
    @sentry_sdk.trace
    def post(self,request):
        try:
            industry_name = request.data.get('industry_name')
            Industry.objects.create(
                name=industry_name
            )
            return Response({'status': True,'message': 'Industry Created Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class CreateUserTypeView(APIView):
    @sentry_sdk.trace
    def post(self,request):
        try:
            user_type_name = request.data.get('user_type_name')
            UserTypes.objects.create(
                name=user_type_name
            )
            return Response({'status': True,'message': 'User Type Created Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
    
class CurrentBrandView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)
            selected_brand = Brands.objects.get(pk=brand_id)
            user = UserRegistration.objects.get(pk=user_id)
            try:
                current_brand_obj = CurrentBrand.objects.get(user_id=user_id)
                current_brand_obj.brand = selected_brand
                current_brand_obj.save()
            except CurrentBrand.DoesNotExist:
                CurrentBrand.objects.create(
                    user=user,
                    brand=selected_brand
                )
            return Response({'status': True,'message': 'Brand Updated Successfully','brand_id':selected_brand.pk}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
# Bluesky

CLIENT_ID = "https://dev.flowkar.com/bluesky/data.json"
REDIRECT_URI = "https://dev.flowkar.com/api/bluesky"
TOKEN_ENDPOINT = "https://bsky.social/oauth/token"

class BlueskyUrlView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        """Handle the callback from Bluesky OAuth"""
        # Get authorization code and state from URL parameters
        code = request.GET.get('code')
        state = request.GET.get('state')
        error = request.GET.get('error')
        error_description = request.GET.get('error_description')
        
        # If there was an error in the OAuth process
        if error:
            return JsonResponse({
                'success': False,
                'error': f"Authentication error: {error} - {error_description}"
            }, status=400)
        code_verifier = 'udaDpYeERtVaQMIIQrwMX6SEhBrCjrUW-bzztI4ngmNAVqKpeH_cC8SRR9HLJ-l52IniMlMjDhVXiFUwHAMAJw'
        try:
            # Generate private key for DPoP
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            
            # Extract public key
            public_key = private_key.public_key()
            
            # Create JWK from public key components
            public_numbers = public_key.public_numbers()
            e_bytes = public_numbers.e.to_bytes((public_numbers.e.bit_length() + 7) // 8, byteorder='big')
            n_bytes = public_numbers.n.to_bytes((public_numbers.n.bit_length() + 7) // 8, byteorder='big')
            
            jwk = {
                'kty': 'RSA',
                'e': base64.urlsafe_b64encode(e_bytes).decode('ascii').rstrip('='),
                'n': base64.urlsafe_b64encode(n_bytes).decode('ascii').rstrip('='),
                'alg': 'RS256',
            }
            
            # Create DPoP proof
            header = {
                'typ': 'dpop+jwt',
                'alg': 'RS256',
                'jwk': jwk
            }
            
            payload = {
                'jti': str(uuid.uuid4()),
                'htm': 'POST',
                'htu': TOKEN_ENDPOINT,
                'iat': int(time.time()),
                'exp': int(time.time()) + 60  # 60 seconds expiration
            }
            
            # Encode header and payload
            encoded_header = base64.urlsafe_b64encode(json.dumps(header).encode()).decode('ascii').rstrip('=')
            encoded_payload = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode('ascii').rstrip('=')
            
            # Create signature
            signature_input = f"{encoded_header}.{encoded_payload}".encode()
            signature = private_key.sign(
                signature_input,
                padding.PKCS1v15(),
                hashes.SHA256()
            )
            
            # Encode signature
            encoded_signature = base64.urlsafe_b64encode(signature).decode('ascii').rstrip('=')
            
            # Full DPoP token
            dpop_proof = f"{encoded_header}.{encoded_payload}.{encoded_signature}"
            
            # Set up token request
            token_data = {
                'client_id': CLIENT_ID,
                'grant_type': 'authorization_code',
                'code': code,
                'redirect_uri': REDIRECT_URI,
                'code_verifier': code_verifier
            }
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'DPoP': dpop_proof
            }
            
            # Make token request with DPoP proof
            response = requests.post(TOKEN_ENDPOINT, data=token_data, headers=headers)
            
            if response.status_code != 200:
                error_msg = "Token exchange failed."
                try:
                    error_data = response.json()
                    if 'error_description' in error_data:
                        error_msg = f"Error: {error_data['error_description']}"
                    elif 'error' in error_data:
                        error_msg = f"Error: {error_data['error']}"
                except:
                    pass
                
                return JsonResponse({
                    'success': False,
                    'error': error_msg
                }, status=400)
            
            # Extract token response
            token_resp = response.json()
            
            # Get user data from id_token if available
            try:
                # Basic user info should be in the token response
                user_data = {
                    'did': token_resp.get('did'),
                    'handle': token_resp.get('handle'),
                    'access_token': token_resp.get('access_token'),
                    'refresh_token': token_resp.get('refresh_token', None),
                    'token_type': token_resp.get('token_type', 'Bearer'),
                    'expires_at': int(time.time()) + int(token_resp.get('expires_in', 3600))
                }
                
                return JsonResponse({
                    'success': True,
                    'message': f"Successfully connected to Bluesky as {user_data['handle']}",
                    'user': user_data
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': f"Error processing user data: {str(e)}"
                }, status=500)
        
        except requests.exceptions.RequestException as e:
            return JsonResponse({
                'success': False,
                'error': f"Connection error: {str(e)}"
            }, status=500)
    
#Beta Tester

class RegisterBetaTesterView(APIView):
    @sentry_sdk.trace
    def post(self,request):
        try:
            name = request.data.get('name')
            email = request.data.get('email')
            age = request.data.get('age')
            mobile = request.data.get('mobile')
            description = request.data.get('description')
            platform_status = request.data.get('platform_status')

            try:
                BetaTester.objects.get(email=email)
                return Response({'status':False,'message':'You Are Already A Beta Tester'},status=status.HTTP_400_BAD_REQUEST)
            except:
                BetaTester.objects.create(
                    name=name,
                    email=email,
                    age=age,
                    mobile=mobile,
                    platform_status=platform_status,
                    description = description if description else ''
                )
                subject = "Welcome to the Flowkar Beta Testing Program! 🚀"
                android_message = f"""Dear {name},

                We're thrilled to have you on board as a beta tester for Flowkar! Your insights and feedback will play a crucial role in shaping the future of our app, and we truly appreciate your time and effort in helping us refine the experience.

                As a valued tester, you'll get exclusive early access to Flowkar, where you can explore its features, test its functionality, and share your valuable feedback. Your input will help us make the app more intuitive, efficient, and impactful for all users.

                Here's what to expect:
                ✅ Early access to the latest version of Flowkar  

                📌 Getting Started on Android: https://play.google.com/apps/testing/com.app.flowkar
                📌 Getting Started on Ios: https://testflight.apple.com/join/RbGs3wm7
                📌 Getting Started on Web: https://beta.flowkar.com

                If you have any questions, feel free to reach out. We're excited to have you as part of this journey and look forward to building something incredible together!

                Welcome to the Flowkar community! 🚀✨  

                Best regards,  
                Milan Katrodiya  
                CEO, Flowkar  
                """
                send_mail(subject,android_message,EMAIL_HOST_USER,[email])

                return Response({'status':True,'message':'Email Sent Successfully'},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class VersionControlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        version_id = request.query_params.get('version_id')
        latest_version = "0.0.0"
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        user_data = UserRegistration.objects.get(pk=user_id)
        email_list = BetaTester.objects.values_list('email', flat=True)

        if user_data.enc_email in email_list:
            try:
                if version_id == "1":
                    version = BetaVersion.objects.get(id=1)
                    platform = "Android"
                elif version_id == "2":
                    version = BetaVersion.objects.get(id=2)
                    platform = "iOS"
                else:
                    return Response({'status': False,'message': 'Invalid ID. Use id=1 for Android or id=2 for iOS.','version': latest_version}, status=status.HTTP_400_BAD_REQUEST)

                if version:
                    latest_version = version.version_number
                    return Response({'status': True,'message': f'{platform} Version Found Successfully','version': latest_version}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False,'message': f'{platform} Version Not Found','version': latest_version}, status=status.HTTP_404_NOT_FOUND)

            except Exception as e:
                return Response({'status': False,'message': f'Error --> {e}','version': latest_version}, status=status.HTTP_400_BAD_REQUEST)
        else:
            try:
                if version_id == "1":
                    version = Version.objects.get(id=1)
                    platform = "Android"
                elif version_id == "2":
                    version = Version.objects.get(id=2)
                    platform = "iOS"
                else:
                    return Response({'status': False,'message': 'Invalid ID. Use id=1 for Android or id=2 for iOS.','version': latest_version}, status=status.HTTP_400_BAD_REQUEST)

                if version:
                    latest_version = version.version_number
                    return Response({'status': True,'message': f'{platform} Version Found Successfully','version': latest_version}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False,'message': f'{platform} Version Not Found','version': latest_version}, status=status.HTTP_404_NOT_FOUND)

            except Exception as e:
                return Response({'status': False,'message': f'Error --> {e}','version': latest_version}, status=status.HTTP_400_BAD_REQUEST)


#User Management 

class UserManagementView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    
    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)
            check_sub = check_user_plan(user_id,int(subscription_id))
            if check_sub['limits']['users_left'] == False:
                return Response({'status': False,'message': 'Please subscribe to a Premuim plan'}, status=status.HTTP_400_BAD_REQUEST)
            
            brands_list = request.data.get('brands_list',[])
            role = request.data.get('role_id')
            invited_user_id = request.data.get('user_id')

            try:
                assigned_role = UserRoles.objects.get(pk=role)
            except UserRoles.DoesNotExist:
                return Response({'status': False,'message': 'Role Not Found'}, status=status.HTTP_400_BAD_REQUEST)
            
            existing_invite_data_list = UserManagement.objects.filter(user_id=user_id, user_invited_id=invited_user_id).values_list('brand_id', flat=True)
            is_existing = False
            for brand_id in brands_list:
                if [brand_id] in existing_invite_data_list:
                    is_existing = True
                    break

            if is_existing:
                return Response({'status':False,'message':"This user has already been invited to the Account with the same role."},status=status.HTTP_400_BAD_REQUEST)
            

            try:
                user = UserRegistration.objects.get(pk=user_id)
                invited_user = UserRegistration.objects.get(pk=invited_user_id)
                onesignal_id = invited_user.onesignal_player if invited_user.onesignal_player else ''

                valid_brands = Brands.objects.filter(pk__in=brands_list)
                valid_brand_ids = list(valid_brands.values_list('pk', flat=True))

                if len(valid_brand_ids) != len(brands_list):
                    return Response({'status': False, 'message': 'One or more selected brands do not exist'}, status=status.HTTP_400_BAD_REQUEST)

                UserManagement.objects.create(
                    user_id=user_id,
                    user_invited_id=invited_user_id,
                    brand_id=brands_list, 
                    permission=role
                )

                brand_names = ','.join(valid_brands.values_list('name', flat=True))
                Notification.objects.create(
                    user=invited_user,
                    from_user=user,
                    type='user_invited',
                    title='You have been invited',
                    message=f'{user.name} has invited you to manage {brand_names}.'
                )
                send_notification(f'{user.name} has invited you to manage {brand_names}.', 'Flowkar', [onesignal_id])
                return Response({'status': True,'message': 'User Invited Successfully'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False,'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
        

class GetInvitedUsersView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user_management = UserManagement.objects.filter(Q(user_id=user_id))
            data = []
            
            for user in user_management:
                user_data = UserRegistration.objects.get(pk=user.user_invited_id)
                user_brands = Brands.objects.filter(pk__in=user.brand_id)
                brand_data = []
                for brand in user_brands:
                    brand_data.append({
                        'id':brand.pk,
                        'name':brand.name,
                        'logo':brand.logo.url if brand.logo else ''
                    })
                
                data.append({
                    'id':user.pk,
                    'name':user_data.name,
                    'username':user_data.username,
                    'email':user_data.enc_email,
                    'profile_image':user_data.profile_picture.url if user_data.profile_picture else '',
                    'brands':brand_data,
                    'is_accepted': user.is_accepted,
                })
            return Response({'status': True,'message': 'Invited Users Found Successfully','data':data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class GetInviteeUsersView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user_management = UserManagement.objects.filter(Q(user_invited_id=user_id))
            data = []
            for user in user_management:
                user_data = UserRegistration.objects.get(pk=user.user_id)
                brand_data = []
                if user.brand_id and isinstance(user.brand_id, list):
                    brands = Brands.objects.filter(id__in=user.brand_id)
                    for brand in brands:
                        brand_data.append({
                            'id': brand.pk,
                            'name': brand.name,
                            'logo': brand.logo.url if brand.logo else ''
                        })
                data.append({
                    'id':user.pk,
                    'name':user_data.name,
                    'username':user_data.username,
                    'email':user_data.enc_email,
                    'profile_image':user_data.profile_picture.url if user_data.profile_picture else '',
                    'is_accepted':user.is_accepted,
                    'brands':brand_data
                })
            return Response({'status': True,'message': 'Invitee Users Found Successfully', 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

            
class CresteUserRoles(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user_roles = UserRoles.objects.filter(Q(user_id=user_id))
            data = []
            for role in user_roles:
                data.append({
                    'id':role.pk,
                    'role_name':role.role_name,
                    'role_description':role.role_description
                })
            return Response({'status': True,'message': 'User Roles Found Successfully','data':data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
    @sentry_sdk.trace
    def post(self,request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            role_name = request.data.get('role_name')
            role_description = request.data.get('role_description')
            UserRoles.objects.create(
                user_id=user_id,
                role_name=role_name,
                role_description=role_description
            )
            return Response({'status': True,'message': 'User Role Created Successfully'}, status=status.HTTP_200_OK)
    
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetPendingInviteUsersView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            pending_users = UserManagement.objects.filter(user_id=user_id, is_accepted=False)

            data = []
            for user in pending_users:
                user_data = UserRegistration.objects.get(pk=user.user_invited_id)
                data.append({
                    'id': user_data.pk,
                    'name': user_data.name,
                    'username': user_data.username,
                    'email': user_data.enc_email,
                    'profile_image': user_data.profile_picture.url if user_data.profile_picture else '',
                })
            return Response({'status': True, 'message': 'Pending Invited Users Found', 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ApproveInviteUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            invite_id = request.data.get('invite_id')
            try:
                user_mgmt = UserManagement.objects.get(pk=invite_id)
            except UserManagement.DoesNotExist:
                return Response({'status': False, 'message': 'No such pending invitation found'}, status=status.HTTP_400_BAD_REQUEST)

            user_mgmt.is_accepted = True
            user_mgmt.save()

            inviter_id = user_mgmt.user_id  
            print(inviter_id)
            invited_user_id = user_mgmt.user_invited_id  
            print(invited_user_id)

            if user_mgmt.brand_id and isinstance(user_mgmt.brand_id, list):
                for brand_id in user_mgmt.brand_id:
                    try:
                        brand = Brands.objects.get(pk=brand_id)
                        if user_id not in brand.user_list:
                            brand.user_list.append(user_id)
                            brand.save()
                    except Brands.DoesNotExist:
                        continue 
            else:
                return Response({'status': False, 'message': 'Invalid brand information'}, status=status.HTTP_400_BAD_REQUEST)
            
            if user_mgmt.user.temp_password == '' or user_mgmt.user.temp_password == None:
                temp_password = generate_temp_password()
                user_mgmt.user.temp_password = temp_password
                user_mgmt.user.save()
            send_mail('Flowkar - User Invite Accepted',f'Thank You For Accepting The Invite. You Can Now Login To The Account And Start Managing It. \n Credentials : \n Email : {user_mgmt.user.enc_email} \n Password : {user_mgmt.user.temp_password}',EMAIL_HOST_USER,[user_mgmt.user_invited.enc_email])
            return Response({'status': True, 'message': 'Accepted Successfully. Email Sent To You With Credentials'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
        

class DeclineInviteView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            decline_id = request.data.get('decline_id')
            
            user_management = UserManagement.objects.get(pk=decline_id)
            
            if user_management.user_invited_id != user_id:
                return Response({'status': False, 'message': 'You are not authorized to decline this invite.'},status=status.HTTP_400_BAD_REQUEST)                
            user_management.delete()
            return Response({'status': True, 'message': 'Invite declined successfully.'},status=status.HTTP_200_OK)
        except UserManagement.DoesNotExist:
            return Response({'status': False, 'message': 'Invite not found.'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class RevokeInviteView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')

            invite_id = request.data.get('invite_id')
            if not invite_id:
                return Response({'status': False, 'message': 'Invite ID is required.'}, status=status.HTTP_400_BAD_REQUEST)

            data = UserManagement.objects.get(
                pk=invite_id
            )
            if data.brand_id and isinstance(data.brand_id, list):
                for brand_id in data.brand_id:
                    try:
                        brand = Brands.objects.get(pk=brand_id)
                        if data.user_invited.pk in brand.user_list:
                            brand.user_list.remove(data.user_invited.pk)
                            brand.save()
                    except Brands.DoesNotExist:
                        continue  
            else:
                return Response({'status': False, 'message': 'Invalid brand information'}, status=status.HTTP_400_BAD_REQUEST)
            data.delete()
            return Response({'status': True, 'message': 'Access revoked successfully.'}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UserDataGetView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            
            user_roles = UserRoles.objects.filter(user_id=user_id)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            data = []
            for role in user_roles:
                users_with_role = UserManagement.objects.filter(permission=str(role.pk))
                
                users_data = []
                for management in users_with_role:
                    try:
                        invited_user = UserRegistration.objects.get(pk=management.user_invited_id)
                        users_data.append({
                            'id': invited_user.pk,
                            'username': invited_user.username or invited_user.name,
                            'profile_picture':  f'{header}{domain}{invited_user.profile_picture.url}'if invited_user.profile_picture else ''
                        })
                    except UserRegistration.DoesNotExist:
                        continue
                
                role_data = {
                    'id': role.pk,
                    'role_name': role.role_name,
                    'role_description': role.role_description,
                    'users': users_data
                }
                
                data.append(role_data)
                
            return Response({'status': True,'message': 'User Roles Found Successfully','data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        

class GetBrandWebDetails(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)

            brands = Brands.objects.filter(user_id=user_id)
            data = []

            if brand_id:
                brands = Brands.objects.filter(pk=brand_id, user_id=user_id)

            for brand in brands:
                try:
                    third_party_data = ThirdPartyAuth.objects.get(brand_id=brand.id)
                except ThirdPartyAuth.DoesNotExist:
                    third_party_data = ThirdPartyAuth.objects.create(brand_id=brand.id)

                facebook = third_party_data.facebook_check
                instagram = third_party_data.instagram_check
                linkedin = third_party_data.linkedin_check
                pinterest = third_party_data.pinterest_check
                vimeo = third_party_data.vimeo_check
                tumblr = third_party_data.tumblr_check
                reddit = third_party_data.reddit_check
                tiktok = third_party_data.tiktok_check
                threads = third_party_data.thread_check
                youtube = third_party_data.youtube_check
                dailymotion = False
                X = False

                check_list = [facebook, instagram, linkedin,
                              pinterest, vimeo, youtube, dailymotion, X]
                is_any_auth = False
                for i in check_list:
                    if i == True:
                        is_any_auth = True
                        break

                append_response = {
                    'id': brand.pk,
                    'name': brand.name,
                    'email': brand.email,
                    'domain': brand.domain,
                    'logo': brand.logo.url if brand.logo else '',
                    'third_party': {
                        'is_any_auth': is_any_auth,
                        'Facebook': facebook,
                        'Instagram': instagram,
                        'Twitter': X,
                        'YouTube': youtube,
                        'LinkedIn': linkedin,
                        'Pinterest': pinterest,
                        'TikTok': tiktok,
                        'Threads': threads,
                        'Dailymotion': dailymotion,
                        'Tumblr': tumblr,
                        'Vimeo': vimeo,
                        'Reddit': reddit
                    }
                }
                data.append(append_response)

            return Response({'status': True, 'message': 'Brand Details Found', 'data': data}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class LogoutView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            try:
                user = UserRegistration.objects.get(pk=user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
            user.onesignal_player = ''
            user.save()
            return Response({'status': True, 'message': 'Logged out successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GetUserTextPostsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = UserProfilePagination
    
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            query_user_id = request.query_params.get('user_id')
            token_user_id = decode_token(auth_token)
            paginator = self.pagination_class()
            
            try:
                domain = request.get_host()
                header = 'https://'  
            except ValueError:
                header = ''
                domain = ''
                
            user_id = query_user_id if query_user_id else token_user_id
            
            try:
                if not UserRegistration.objects.filter(pk=user_id).exists():
                    return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({'status': False, 'message': f'Error validating user: {e}'}, status=status.HTTP_400_BAD_REQUEST)
            
            post_queryset = Post.objects.filter(
                user_id=user_id, 
                is_deleted=False, 
                is_posted=True,
                is_text_post=True  
            ).select_related(
                'user'  
            ).prefetch_related(
                Prefetch(
                    'comment_set',  
                    queryset=Comment.objects.select_related('user').order_by('-pk'),
                    to_attr='prefetched_comments'
                )
            ).annotate(
                likes_count=Count('likepost'),  
                is_liked=Exists(
                    LikePost.objects.filter(
                        post_id=OuterRef('pk'),
                        user_id=token_user_id
                    )
                ),
                is_saved=Exists(
                    SavedPost.objects.filter(
                        post_id=OuterRef('pk'),
                        user_id=token_user_id
                    )
                )
            ).order_by('-created_at')
            
            all_data = []
            for post in post_queryset:
                latest_comment = ''
                if hasattr(post, 'prefetched_comments') and post.prefetched_comments:
                    comment = post.prefetched_comments[0]
                    latest_comment = f'{comment.user.username} {comment.comment_text}'
                
                append_object = {
                    'id': post.pk,
                    'title': post.title,
                    'description': post.description,
                    'location': post.location,
                    'likes': post.likes_count,
                    'dislikes': post.dislikes,
                    'comments_count': post.comments_count,
                    'tagged_in': post.tagged_in,
                    'created_at': post.created_at,
                    'latest_comment': latest_comment,
                    'user': {
                        'user_id': post.user.pk,
                        'username': post.user.username,
                        'name': post.user.name,
                        'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                    },
                    'is_liked': post.is_liked,
                    'is_saved': post.is_saved,
                    'is_text_post': post.is_text_post
                }
                all_data.append(append_object)
            
            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Text Posts Found Successfully', 'data': result_page})
                
        except Exception as e:
            return Response({'status': False, 'message': f'Error: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)


# X Purchase
class PurchaseXView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            amount = fun_dollar_to_inr(10)*100
            user = UserRegistration.objects.get(pk=user_id)
            try:
                existing_special_social = SpecialSocial.objects.get(user_id=user_id)
                if existing_special_social.is_active:
                    return Response({'status': False, 'message': 'You are already subscribed to X.'}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    payment_link = create_payment_link(auth_token,int(amount), user.name,user.enc_email)
                    return Response({'status': True, 'message': 'Payment link created successfully', 'data': payment_link}, status=status.HTTP_200_OK)
            except SpecialSocial.DoesNotExist:
                payment_link = create_payment_link(auth_token,int(amount), user.name,user.enc_email)
                return Response({'status': True, 'message': 'Payment link created successfully', 'data': payment_link}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class DisconnectXView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            try:
                user = ThirdPartyAuth.objects.get(brand_id=brand_id)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Brand not found'}, status=status.HTTP_400_BAD_REQUEST)
            user.x_check = False
            user.x_access_token = ''
            user.x_refresh_token = ''
            user.save()
            return Response({'status': True, 'message': 'X disconnected successfully','Connect':False}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class MastodonUrlView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            instance_url = request.data.get('instance_url')

            if not instance_url:
                return Response({'status': False, 'message': 'Mastodon instance URL is required'}, status=status.HTTP_400_BAD_REQUEST)

            app_data = register_mastodon_app(instance_url)
            if not app_data:
                return Response({'status': False, 'message': 'Failed to register app with Mastodon instance'}, status=status.HTTP_400_BAD_REQUEST)

            state_data = f"{brand_id}|{app_data['client_id']}|{app_data['client_secret']}|{instance_url}"

            # Generate authorization URL
            auth_url = generate_mastodon_auth_url(instance_url, app_data['client_id'], state_data)
            if not auth_url:
                return Response({'status': False, 'message': 'Failed to generate authorization URL'}, status=status.HTTP_400_BAD_REQUEST)

            return Response({'status': True, 'message': 'Mastodon authorization URL generated', 'url': auth_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class MastodonCallbackView(APIView):
    @sentry_sdk.trace
    def get(self, request):
        try:
            code = request.GET.get('code')
            state = request.GET.get('state')

            if not code or not state:
                return redirect('https://api.flowkar.com/api/social-connect-deeplink/error')

            # Parse state data
            try:
                brand_id, client_id, client_secret, instance_url = state.split('|')
            except ValueError:
                return redirect('https://api.flowkar.com/api/social-connect-deeplink/error')

            # Exchange code for access token
            access_token = exchange_code_for_token(instance_url, client_id, client_secret, code)
            if not access_token:
                # Redirect to brand page with error parameter so user can try again
                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}?error=mastodon_token_failed')

            # Get user information
            user_info = get_mastodon_user_info(instance_url, access_token)
            if not user_info:
                # Redirect to brand page with error parameter so user can try again
                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}?error=mastodon_user_failed')

            print('user_info',user_info)
            print('access_token',access_token)
            print('instance_url',instance_url)
            print(user_info['id'])
            print("state",state)

            user_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
            user_data.mastodon_check = True
            user_data.mastodon_token = access_token
            user_data.mastodon_user_id = user_info['id']
            user_data.save()

            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        except Exception as e:
            return redirect('https://api.flowkar.com/api/social-connect-deeplink/error')


class MastodonDisconnectView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')

            try:
                user = ThirdPartyAuth.objects.get(brand_id=brand_id)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Brand not found'}, status=status.HTTP_400_BAD_REQUEST)

            user.mastodon_check = False
            user.mastodon_token = ''
            user.mastodon_user_id = ''
            user.save()

            return Response({'status': True, 'message': 'Mastodon disconnected successfully', 'Connect': False}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


@csrf_exempt
def RazorpayCallbackView(request):
    brand_id = None  # Initialize brand_id
    try:
        event_data = {
            'razorpay_payment_id': request.GET.get('razorpay_payment_id'),
            'razorpay_payment_link_id': request.GET.get('razorpay_payment_link_id'),
            'razorpay_payment_link_reference_id': request.GET.get('razorpay_payment_link_reference_id'),
            'razorpay_payment_link_status': request.GET.get('razorpay_payment_link_status'),
            'razorpay_signature': request.GET.get('razorpay_signature')
        }
        
        # Get payment data first to extract user_id
        payment_data = get_payment_status(event_data['razorpay_payment_id'])
        user_id = decode_token(payment_data['notes']['token'])
        
        # Get brand_id early in the process
        try:
            current_brand = CurrentBrand.objects.get(user_id=user_id)
            brand_id = current_brand.brand.pk
        except CurrentBrand.DoesNotExist:
            user_brands = Brands.objects.filter(user_id=user_id).first()
            if user_brands:
                brand_id = user_brands.pk
            else:
                # Handle case where user has no brands
                # You might want to redirect to a different URL or handle this case
                return JsonResponse({'error': 'No brand found for user'}, status=200)
        
        # Process payment status
        if event_data['razorpay_payment_link_status'] == 'paid':
            try:
                existing_special_social = SpecialSocial.objects.get(user_id=user_id)
                if existing_special_social.is_active:
                    return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
                else:
                    existing_special_social.is_active = True
                    existing_special_social.created_at = timezone.now()
                    existing_special_social.save()
                    return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
            except SpecialSocial.DoesNotExist:
                SpecialSocial.objects.create(user_id=user_id, is_active=True)
                return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        else:
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
            
    except Exception as e:
        # Log the error for debugging
        print(f"Error in RazorpayCallbackView: {str(e)}")
        
        # Only redirect with brand_id if it was successfully retrieved
        if brand_id:
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{brand_id}')
        else:
            # Fallback redirect or error response
            return JsonResponse({'error': 'Payment processing failed'}, status=200)
        

class SwitchUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            logged_in_user = request.headers.get('user')
            onesignal_player = request.headers.get('onesignalplayer')
            user_id = decode_token(auth_token)

            try:                
                check_user = int(logged_in_user)
            except (TypeError, ValueError):
                check_user = ""
            try:
                domain = request.get_host()
                header = 'https://'  
            except ValueError:
                header = ''
                domain = ''
            try:
                user = UserRegistration.objects.get(pk=logged_in_user)
                brand = Brands.objects.get(pk=brand_id)
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                profile_picture = f"{header}{domain}{brand.user.profile_picture.url}" if brand.user.profile_picture else ''
                
                if user.pk in brand.user_list or check_user == brand.user.pk:
                    facebook = third_party_data.facebook_check
                    instagram = third_party_data.instagram_check
                    x = third_party_data.x_check
                    youtube = third_party_data.youtube_check
                    linkedin = third_party_data.linkedin_check
                    pinterest = third_party_data.pinterest_check
                    tiktok = third_party_data.tiktok_check
                    threads = third_party_data.thread_check
                    dailymotion = False
                    twitter = False
                    mastodon = third_party_data.mastodon_check
                    telegram = third_party_data.telegram_check
                    tumblr = third_party_data.tumblr_check
                    vimeo = third_party_data.vimeo_check
                    reddit = third_party_data.reddit_check

                    check_list = [facebook, instagram, linkedin,
                                pinterest, vimeo, youtube, dailymotion, x, twitter, tumblr, reddit, tiktok, threads]
                    is_any_auth = False
                    for i in check_list:
                        if i == True:
                            is_any_auth = True
                        else:
                            pass

                    token = generate_jwt(brand.user)
                    if onesignal_player:
                        brand.user.onesignal_player = onesignal_player
                    brand.user.save()

                    response_data = {
                        'user_status':brand.user.current_status,
                        'user_id': brand.user.pk,
                        'username': brand.user.username, 
                        'name': brand.user.name, 
                        'profile_image': profile_picture, 
                        'token': token, 
                        'is_any_auth': is_any_auth, 
                        'Facebook': facebook, 
                        'Instagram': instagram, 
                        'x': x, 
                        'YouTube': youtube, 
                        'LinkedIn': linkedin, 
                        'Pinterest': pinterest, 
                        'tiktok': tiktok, 
                        'threads': threads, 
                        'Dailymotion': dailymotion, 
                        "Twitter":twitter,
                        'tumblr': tumblr, 
                        'Vimeo': vimeo, 
                        'reddit': reddit, 
                        'telegram':telegram,
                        'mastodon':mastodon,
                        'is_admin': brand.user.is_admin,
                        'sub_data':{}
                    }
                    return Response({'status': True, 'message': 'User switched successfully','data':response_data}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': f'You are not authorized to switch user',}, status=status.HTTP_400_BAD_REQUEST)
            except (UserRegistration.DoesNotExist, Brands.DoesNotExist):
                return Response({'status': False, 'message': 'User or Brand not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

#Telegram

class TelegramSendCodeView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            phone_number = request.data.get('phone_number')
            if not phone_number:
                return Response({
                    'status': False,
                    'message': 'Phone number is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            session_name = f'telegram_session_{user_id}_{brand_id}'

            result = send_telegram_code(phone_number, session_name)

            if result['success']:

                return Response({
                    'status': True,
                    'message': result['message'],
                    'phone_code_hash': result.get('phone_code_hash')
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': False,
                    'message': result['message'],
                    'error': result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class TelegramSignInView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            phone_number = request.data.get('phone_number')
            code = request.data.get('code')
            password = request.data.get('password')
            phone_code_hash = request.data.get('phone_code_hash')# For 2FA

            if not all([phone_number, code, phone_code_hash]):
                return Response({
                    'status': False,
                    'message': 'Phone number, code, and phone_code_hash are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            session_name = f'telegram_session_{user_id}_{brand_id}'

            result = telegram_sign_in(phone_number, code, phone_code_hash,password,session_name)

            if result['success']:
                third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
                third_party_auth.telegram_check = True
                third_party_auth.save()

                return Response({
                    'status': True,
                    'message': result['message'],
                    'user_info': {
                        'user_id': result.get('user_id'),
                        'username': result.get('username'),
                        'first_name': result.get('first_name'),
                        'last_name': result.get('last_name'),
                        'phone': result.get('phone')
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': False,
                    'message': result['message'],
                    'requires_password': result.get('requires_password', False),
                    'error': result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


# Ghost Blog Platform Views
class GhostAuthView(APIView):
    """Ghost blog authentication view"""
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            ghost_url = request.data.get('ghost_url')
            admin_api_key = request.data.get('admin_api_key')

            if not ghost_url or not admin_api_key:
                return Response({
                    'status': False,
                    'message': 'Ghost URL and Admin API key are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate Ghost credentials
            success, message, site_info = validate_ghost_credentials(ghost_url, admin_api_key)

            if not success:
                return Response({
                    'status': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)

            # Save Ghost credentials
            try:
                third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
            except ThirdPartyAuth.DoesNotExist:
                third_party_auth = ThirdPartyAuth.objects.create(brand_id=brand_id)

            third_party_auth.ghost_check = True
            third_party_auth.ghost_url = ghost_url
            third_party_auth.ghost_admin_api_key = admin_api_key
            third_party_auth.ghost_site_title = site_info.get('title', '') if site_info else ''
            third_party_auth.save()

            return Response({
                'status': True,
                'message': 'Ghost blog connected successfully',
                'data': {
                    'site_title': site_info.get('title', '') if site_info else '',
                    'site_url': ghost_url,
                    'connected': True
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class GhostDisconnectView(APIView):
    """Ghost blog disconnect view"""
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            try:
                third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
                third_party_auth.ghost_check = False
                third_party_auth.ghost_url = ''
                third_party_auth.ghost_admin_api_key = ''
                third_party_auth.ghost_site_title = ''
                third_party_auth.save()

                return Response({
                    'status': True,
                    'message': 'Ghost blog disconnected successfully'
                }, status=status.HTTP_200_OK)

            except ThirdPartyAuth.DoesNotExist:
                return Response({
                    'status': False,
                    'message': 'No Ghost connection found'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class GhostAnalyticsView(APIView):
    """Ghost blog analytics view"""
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            try:
                third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if not third_party_auth.ghost_check:
                    return Response({
                        'status': False,
                        'message': 'Ghost blog not connected'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Get Ghost analytics
                success, message, analytics_data = get_ghost_analytics(
                    third_party_auth.ghost_url,
                    third_party_auth.ghost_admin_api_key
                )

                if success:
                    return Response({
                        'status': True,
                        'message': 'Analytics retrieved successfully',
                        'data': analytics_data
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({
                        'status': False,
                        'message': message
                    }, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({
                    'status': False,
                    'message': 'No Ghost connection found'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)