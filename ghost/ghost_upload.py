"""
Ghost Blog Upload Modu<PERSON>
Handles uploading blog posts to Ghost platform
"""

import requests
import json
import os
from typing import Dict, List, Optional, Tuple
from .ghost_auth import GhostAuth


class GhostBlogUploader:
    """Ghost blog post uploader"""
    
    def __init__(self, ghost_url: str, admin_api_key: str):
        """
        Initialize Ghost blog uploader
        
        Args:
            ghost_url: Ghost blog URL
            admin_api_key: Ghost Admin API key
        """
        self.ghost_auth = GhostAuth(ghost_url, admin_api_key)
        self.api_url = f"{ghost_url.rstrip('/')}/ghost/api/admin"
    
    def upload_image(self, image_path: str) -> Optional[str]:
        """
        Upload image to Ghost
        
        Args:
            image_path: Path to image file
            
        Returns:
            Image URL or None if failed
        """
        try:
            headers = self.ghost_auth.get_headers()
            # Remove Content-Type for file upload
            headers.pop('Content-Type', None)
            
            with open(image_path, 'rb') as image_file:
                files = {'file': image_file}
                response = requests.post(
                    f"{self.api_url}/images/upload/",
                    headers=headers,
                    files=files
                )
            
            if response.status_code == 201:
                return response.json().get('images', [{}])[0].get('url')
            else:
                print(f"Image upload failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Error uploading image: {e}")
            return None
    
    def create_post(self, 
                   title: str, 
                   content: str, 
                   excerpt: Optional[str] = None,
                   tags: Optional[List[str]] = None,
                   featured_image: Optional[str] = None,
                   status: str = 'draft',
                   featured: bool = False,
                   meta_title: Optional[str] = None,
                   meta_description: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        Create a blog post on Ghost
        
        Args:
            title: Post title
            content: Post content (HTML or Markdown)
            excerpt: Post excerpt
            tags: List of tag names
            featured_image: Featured image URL
            status: Post status ('draft' or 'published')
            featured: Whether post is featured
            meta_title: SEO meta title
            meta_description: SEO meta description
            
        Returns:
            Tuple of (success, message, post_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            # Prepare post data
            post_data = {
                'title': title,
                'html': content,
                'status': status,
                'featured': featured
            }
            
            # Add optional fields
            if excerpt:
                post_data['excerpt'] = excerpt
            
            if featured_image:
                post_data['feature_image'] = featured_image
            
            if meta_title:
                post_data['meta_title'] = meta_title
            
            if meta_description:
                post_data['meta_description'] = meta_description
            
            # Handle tags
            if tags:
                post_data['tags'] = [{'name': tag} for tag in tags]
            
            # Create the post
            payload = {'posts': [post_data]}
            
            response = requests.post(
                f"{self.api_url}/posts/",
                headers=headers,
                data=json.dumps(payload)
            )
            
            if response.status_code == 201:
                post_response = response.json()
                post = post_response.get('posts', [{}])[0]
                return True, "Post created successfully", post
            else:
                error_msg = f"Failed to create post: {response.status_code} - {response.text}"
                return False, error_msg, None
                
        except Exception as e:
            return False, f"Error creating post: {str(e)}", None
    
    def update_post(self, 
                   post_id: str,
                   title: Optional[str] = None, 
                   content: Optional[str] = None, 
                   excerpt: Optional[str] = None,
                   tags: Optional[List[str]] = None,
                   featured_image: Optional[str] = None,
                   status: Optional[str] = None,
                   featured: Optional[bool] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        Update an existing blog post on Ghost
        
        Args:
            post_id: Ghost post ID
            title: Post title
            content: Post content
            excerpt: Post excerpt
            tags: List of tag names
            featured_image: Featured image URL
            status: Post status
            featured: Whether post is featured
            
        Returns:
            Tuple of (success, message, post_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            # Get current post data first
            get_response = requests.get(
                f"{self.api_url}/posts/{post_id}/",
                headers=headers
            )
            
            if get_response.status_code != 200:
                return False, "Post not found", None
            
            current_post = get_response.json().get('posts', [{}])[0]
            
            # Prepare update data
            post_data = {
                'updated_at': current_post.get('updated_at')  # Required for updates
            }
            
            # Update only provided fields
            if title is not None:
                post_data['title'] = title
            if content is not None:
                post_data['html'] = content
            if excerpt is not None:
                post_data['excerpt'] = excerpt
            if featured_image is not None:
                post_data['feature_image'] = featured_image
            if status is not None:
                post_data['status'] = status
            if featured is not None:
                post_data['featured'] = featured
            
            # Handle tags
            if tags is not None:
                post_data['tags'] = [{'name': tag} for tag in tags]
            
            # Update the post
            payload = {'posts': [post_data]}
            
            response = requests.put(
                f"{self.api_url}/posts/{post_id}/",
                headers=headers,
                data=json.dumps(payload)
            )
            
            if response.status_code == 200:
                post_response = response.json()
                post = post_response.get('posts', [{}])[0]
                return True, "Post updated successfully", post
            else:
                error_msg = f"Failed to update post: {response.status_code} - {response.text}"
                return False, error_msg, None
                
        except Exception as e:
            return False, f"Error updating post: {str(e)}", None
    
    def get_posts(self, limit: int = 15, page: int = 1) -> Tuple[bool, str, Optional[List[Dict]]]:
        """
        Get list of posts from Ghost
        
        Args:
            limit: Number of posts to retrieve
            page: Page number
            
        Returns:
            Tuple of (success, message, posts_list)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            params = {
                'limit': limit,
                'page': page,
                'include': 'tags,authors'
            }
            
            response = requests.get(
                f"{self.api_url}/posts/",
                headers=headers,
                params=params
            )
            
            if response.status_code == 200:
                posts_data = response.json()
                posts = posts_data.get('posts', [])
                return True, "Posts retrieved successfully", posts
            else:
                error_msg = f"Failed to get posts: {response.status_code} - {response.text}"
                return False, error_msg, None
                
        except Exception as e:
            return False, f"Error getting posts: {str(e)}", None
    
    def delete_post(self, post_id: str) -> Tuple[bool, str]:
        """
        Delete a post from Ghost
        
        Args:
            post_id: Ghost post ID
            
        Returns:
            Tuple of (success, message)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            response = requests.delete(
                f"{self.api_url}/posts/{post_id}/",
                headers=headers
            )
            
            if response.status_code == 204:
                return True, "Post deleted successfully"
            else:
                error_msg = f"Failed to delete post: {response.status_code} - {response.text}"
                return False, error_msg
                
        except Exception as e:
            return False, f"Error deleting post: {str(e)}"


def upload_ghost_blog(ghost_url: str, 
                     admin_api_key: str,
                     title: str, 
                     content: str, 
                     **kwargs) -> Tuple[bool, str, Optional[Dict]]:
    """
    Convenience function to upload a blog post to Ghost
    
    Args:
        ghost_url: Ghost blog URL
        admin_api_key: Ghost Admin API key
        title: Post title
        content: Post content
        **kwargs: Additional post parameters
        
    Returns:
        Tuple of (success, message, post_data)
    """
    try:
        uploader = GhostBlogUploader(ghost_url, admin_api_key)
        return uploader.create_post(title, content, **kwargs)
    except Exception as e:
        return False, f"Upload error: {str(e)}", None
