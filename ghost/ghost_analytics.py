"""
Ghost Blog Analytics Module
Handles retrieving analytics and statistics from Ghost platform
"""

import requests
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from .ghost_auth import GhostAuth


class GhostAnalytics:
    """Ghost analytics handler"""
    
    def __init__(self, ghost_url: str, admin_api_key: str):
        """
        Initialize Ghost analytics
        
        Args:
            ghost_url: Ghost blog URL
            admin_api_key: Ghost Admin API key
        """
        self.ghost_auth = GhostAuth(ghost_url, admin_api_key)
        self.api_url = f"{ghost_url.rstrip('/')}/ghost/api/admin"
    
    def get_site_stats(self) -> Tuple[bool, str, Optional[Dict]]:
        """
        Get basic site statistics
        
        Returns:
            Tuple of (success, message, stats_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            # Get posts count
            posts_response = requests.get(
                f"{self.api_url}/posts/",
                headers=headers,
                params={'limit': 1}
            )
            
            # Get pages count
            pages_response = requests.get(
                f"{self.api_url}/pages/",
                headers=headers,
                params={'limit': 1}
            )
            
            # Get tags count
            tags_response = requests.get(
                f"{self.api_url}/tags/",
                headers=headers,
                params={'limit': 1}
            )
            
            stats = {}
            
            if posts_response.status_code == 200:
                posts_data = posts_response.json()
                stats['total_posts'] = posts_data.get('meta', {}).get('pagination', {}).get('total', 0)
            
            if pages_response.status_code == 200:
                pages_data = pages_response.json()
                stats['total_pages'] = pages_data.get('meta', {}).get('pagination', {}).get('total', 0)
            
            if tags_response.status_code == 200:
                tags_data = tags_response.json()
                stats['total_tags'] = tags_data.get('meta', {}).get('pagination', {}).get('total', 0)
            
            return True, "Stats retrieved successfully", stats
            
        except Exception as e:
            return False, f"Error getting stats: {str(e)}", None
    
    def get_recent_posts(self, limit: int = 10) -> Tuple[bool, str, Optional[List[Dict]]]:
        """
        Get recent posts with basic analytics
        
        Args:
            limit: Number of recent posts to retrieve
            
        Returns:
            Tuple of (success, message, posts_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            response = requests.get(
                f"{self.api_url}/posts/",
                headers=headers,
                params={
                    'limit': limit,
                    'order': 'published_at DESC',
                    'include': 'tags,authors',
                    'filter': 'status:published'
                }
            )
            
            if response.status_code == 200:
                posts_data = response.json()
                posts = posts_data.get('posts', [])
                
                # Add basic analytics for each post
                for post in posts:
                    post['analytics'] = {
                        'published_date': post.get('published_at'),
                        'updated_date': post.get('updated_at'),
                        'word_count': len(post.get('plaintext', '').split()) if post.get('plaintext') else 0,
                        'reading_time': post.get('reading_time', 0),
                        'featured': post.get('featured', False),
                        'tags_count': len(post.get('tags', []))
                    }
                
                return True, "Recent posts retrieved successfully", posts
            else:
                error_msg = f"Failed to get recent posts: {response.status_code} - {response.text}"
                return False, error_msg, None
                
        except Exception as e:
            return False, f"Error getting recent posts: {str(e)}", None
    
    def get_posts_by_status(self) -> Tuple[bool, str, Optional[Dict]]:
        """
        Get posts count by status
        
        Returns:
            Tuple of (success, message, status_counts)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            statuses = ['published', 'draft', 'scheduled']
            status_counts = {}
            
            for status in statuses:
                response = requests.get(
                    f"{self.api_url}/posts/",
                    headers=headers,
                    params={
                        'limit': 1,
                        'filter': f'status:{status}'
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    status_counts[status] = data.get('meta', {}).get('pagination', {}).get('total', 0)
                else:
                    status_counts[status] = 0
            
            return True, "Status counts retrieved successfully", status_counts
            
        except Exception as e:
            return False, f"Error getting status counts: {str(e)}", None
    
    def get_popular_tags(self, limit: int = 10) -> Tuple[bool, str, Optional[List[Dict]]]:
        """
        Get popular tags with post counts
        
        Args:
            limit: Number of tags to retrieve
            
        Returns:
            Tuple of (success, message, tags_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            response = requests.get(
                f"{self.api_url}/tags/",
                headers=headers,
                params={
                    'limit': limit,
                    'order': 'count.posts DESC',
                    'include': 'count.posts'
                }
            )
            
            if response.status_code == 200:
                tags_data = response.json()
                tags = tags_data.get('tags', [])
                
                # Format tag data for analytics
                formatted_tags = []
                for tag in tags:
                    formatted_tags.append({
                        'id': tag.get('id'),
                        'name': tag.get('name'),
                        'slug': tag.get('slug'),
                        'description': tag.get('description'),
                        'post_count': tag.get('count', {}).get('posts', 0),
                        'visibility': tag.get('visibility', 'public')
                    })
                
                return True, "Popular tags retrieved successfully", formatted_tags
            else:
                error_msg = f"Failed to get popular tags: {response.status_code} - {response.text}"
                return False, error_msg, None
                
        except Exception as e:
            return False, f"Error getting popular tags: {str(e)}", None
    
    def get_monthly_post_counts(self, months: int = 12) -> Tuple[bool, str, Optional[List[Dict]]]:
        """
        Get post counts for the last N months
        
        Args:
            months: Number of months to analyze
            
        Returns:
            Tuple of (success, message, monthly_data)
        """
        try:
            headers = self.ghost_auth.get_headers()
            
            monthly_data = []
            current_date = datetime.now()
            
            for i in range(months):
                # Calculate month start and end dates
                month_start = current_date.replace(day=1) - timedelta(days=i*30)
                month_end = month_start.replace(day=28) + timedelta(days=4)
                month_end = month_end - timedelta(days=month_end.day)
                
                # Format dates for Ghost API
                start_str = month_start.strftime('%Y-%m-%d')
                end_str = month_end.strftime('%Y-%m-%d')
                
                # Get posts for this month
                response = requests.get(
                    f"{self.api_url}/posts/",
                    headers=headers,
                    params={
                        'limit': 1,
                        'filter': f'published_at:>={start_str}+published_at:<={end_str}+status:published'
                    }
                )
                
                post_count = 0
                if response.status_code == 200:
                    data = response.json()
                    post_count = data.get('meta', {}).get('pagination', {}).get('total', 0)
                
                monthly_data.append({
                    'month': month_start.strftime('%Y-%m'),
                    'month_name': month_start.strftime('%B %Y'),
                    'post_count': post_count,
                    'start_date': start_str,
                    'end_date': end_str
                })
            
            # Reverse to get chronological order
            monthly_data.reverse()
            
            return True, "Monthly post counts retrieved successfully", monthly_data
            
        except Exception as e:
            return False, f"Error getting monthly post counts: {str(e)}", None
    
    def get_content_analytics(self) -> Tuple[bool, str, Optional[Dict]]:
        """
        Get content analytics summary
        
        Returns:
            Tuple of (success, message, analytics_data)
        """
        try:
            # Get basic stats
            stats_success, stats_msg, stats_data = self.get_site_stats()
            
            # Get status counts
            status_success, status_msg, status_data = self.get_posts_by_status()
            
            # Get popular tags
            tags_success, tags_msg, tags_data = self.get_popular_tags(5)
            
            # Get recent posts
            posts_success, posts_msg, posts_data = self.get_recent_posts(5)
            
            analytics = {
                'site_stats': stats_data if stats_success else {},
                'post_status_counts': status_data if status_success else {},
                'popular_tags': tags_data if tags_success else [],
                'recent_posts': posts_data if posts_success else [],
                'generated_at': datetime.now().isoformat()
            }
            
            return True, "Content analytics retrieved successfully", analytics
            
        except Exception as e:
            return False, f"Error getting content analytics: {str(e)}", None


def get_ghost_analytics(ghost_url: str, admin_api_key: str) -> Tuple[bool, str, Optional[Dict]]:
    """
    Convenience function to get Ghost analytics
    
    Args:
        ghost_url: Ghost blog URL
        admin_api_key: Ghost Admin API key
        
    Returns:
        Tuple of (success, message, analytics_data)
    """
    try:
        analytics = GhostAnalytics(ghost_url, admin_api_key)
        return analytics.get_content_analytics()
    except Exception as e:
        return False, f"Analytics error: {str(e)}", None
