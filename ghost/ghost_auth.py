"""
Ghost Blog Authentication Module
Handles Ghost Admin API authentication using API keys
"""

import requests
import jwt
import datetime
from typing import Dict, <PERSON>ple, Optional


class GhostAuth:
    """Ghost authentication handler using Admin API"""
    
    def __init__(self, ghost_url: str, admin_api_key: str):
        """
        Initialize Ghost authentication
        
        Args:
            ghost_url: Ghost blog URL (e.g., https://yourblog.ghost.io)
            admin_api_key: Ghost Admin API key
        """
        self.ghost_url = ghost_url.rstrip('/')
        self.admin_api_key = admin_api_key
        self.api_url = f"{self.ghost_url}/ghost/api/admin"
    
    def generate_jwt_token(self) -> str:
        """
        Generate JWT token for Ghost Admin API authentication
        
        Returns:
            JWT token string
        """
        # Split the key into ID and secret
        key_id, secret = self.admin_api_key.split(':')
        
        # Create JWT payload
        iat = int(datetime.datetime.now().timestamp())
        exp = iat + 300  # Token expires in 5 minutes
        
        payload = {
            'iat': iat,
            'exp': exp,
            'aud': '/admin/'
        }
        
        # Generate JWT token
        token = jwt.encode(
            payload,
            bytes.fromhex(secret),
            algorithm='HS256',
            headers={'kid': key_id}
        )
        
        return token
    
    def get_headers(self) -> Dict[str, str]:
        """
        Get headers for Ghost API requests
        
        Returns:
            Dictionary of headers
        """
        token = self.generate_jwt_token()
        return {
            'Authorization': f'Ghost {token}',
            'Content-Type': 'application/json',
            'Accept-Version': 'v5.0'
        }
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        Test Ghost API connection
        
        Returns:
            Tuple of (success, message)
        """
        try:
            headers = self.get_headers()
            response = requests.get(f"{self.api_url}/site/", headers=headers)
            
            if response.status_code == 200:
                site_data = response.json()
                site_title = site_data.get('site', {}).get('title', 'Unknown')
                return True, f"Successfully connected to Ghost blog: {site_title}"
            else:
                return False, f"Failed to connect: {response.status_code} - {response.text}"
                
        except Exception as e:
            return False, f"Connection error: {str(e)}"
    
    def get_site_info(self) -> Optional[Dict]:
        """
        Get Ghost site information
        
        Returns:
            Site information dictionary or None if failed
        """
        try:
            headers = self.get_headers()
            response = requests.get(f"{self.api_url}/site/", headers=headers)
            
            if response.status_code == 200:
                return response.json().get('site', {})
            return None
            
        except Exception as e:
            print(f"Error getting site info: {e}")
            return None


def validate_ghost_credentials(ghost_url: str, admin_api_key: str) -> Tuple[bool, str, Optional[Dict]]:
    """
    Validate Ghost credentials and return site info
    
    Args:
        ghost_url: Ghost blog URL
        admin_api_key: Ghost Admin API key
        
    Returns:
        Tuple of (success, message, site_info)
    """
    try:
        ghost_auth = GhostAuth(ghost_url, admin_api_key)
        success, message = ghost_auth.test_connection()
        
        if success:
            site_info = ghost_auth.get_site_info()
            return True, message, site_info
        else:
            return False, message, None
            
    except Exception as e:
        return False, f"Validation error: {str(e)}", None


def get_ghost_auth_headers(ghost_url: str, admin_api_key: str) -> Optional[Dict[str, str]]:
    """
    Get Ghost authentication headers
    
    Args:
        ghost_url: Ghost blog URL
        admin_api_key: Ghost Admin API key
        
    Returns:
        Headers dictionary or None if failed
    """
    try:
        ghost_auth = GhostAuth(ghost_url, admin_api_key)
        return ghost_auth.get_headers()
    except Exception as e:
        print(f"Error generating headers: {e}")
        return None
