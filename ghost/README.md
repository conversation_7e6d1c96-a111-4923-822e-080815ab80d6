# Ghost Blog Platform Integration

This module provides complete integration with Ghost blog platform for uploading and managing blog posts.

## Features

- **Authentication**: Connect to Ghost blog using Admin API key
- **Blog Upload**: Upload blog posts with content, images, and metadata
- **Analytics**: Retrieve blog statistics and analytics
- **Management**: Update, delete, and manage blog posts

## API Endpoints

### Authentication

#### Connect Ghost Blog
```
POST /api/ghost-auth/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>

Body:
{
  "ghost_url": "https://yourblog.ghost.io",
  "admin_api_key": "your_admin_api_key"
}
```

#### Disconnect Ghost Blog
```
POST /api/disconnect-ghost/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>
```

#### Get Ghost Analytics
```
GET /api/ghost-analytics/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>
```

### Blog Management

#### Upload Blog Post
```
POST /api/ghost-blog-upload/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>

Body:
{
  "title": "Blog Post Title",
  "content": "<p>Blog post content in HTML</p>",
  "excerpt": "Short description",
  "tags": ["tag1", "tag2"],
  "status": "draft", // or "published"
  "featured": false,
  "meta_title": "SEO Title",
  "meta_description": "SEO Description"
}

Files:
  featured_image: (optional) Image file for featured image
```

## Setup Instructions

### 1. Get Ghost Admin API Key

1. Go to your Ghost Admin panel
2. Navigate to Settings > Integrations
3. Click "Add custom integration"
4. Copy the Admin API Key

### 2. Connect Your Blog

Use the `/api/ghost-auth/` endpoint with your Ghost URL and Admin API key.

### 3. Upload Blog Posts

Use the `/api/ghost-blog-upload/` endpoint to upload blog posts.

## Database Fields

The following fields are added to the `ThirdPartyAuth` model:

- `ghost_check`: Boolean indicating if Ghost is connected
- `ghost_url`: Ghost blog URL
- `ghost_admin_api_key`: Ghost Admin API key
- `ghost_site_title`: Ghost site title

## File Structure

```
ghost/
├── __init__.py
├── ghost_auth.py          # Authentication handling
├── ghost_upload.py        # Blog upload functionality
├── ghost_analytics.py     # Analytics and statistics
└── README.md             # This documentation
```

## Usage Examples

### Python Usage

```python
from ghost.ghost_upload import upload_ghost_blog

# Upload a blog post
success, message, post_data = upload_ghost_blog(
    ghost_url="https://yourblog.ghost.io",
    admin_api_key="your_api_key",
    title="My Blog Post",
    content="<p>Blog content here</p>",
    status="published"
)
```

### API Usage

```javascript
// Connect Ghost blog
const response = await fetch('/api/ghost-auth/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token',
    'brand': 'brand_id',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    ghost_url: 'https://yourblog.ghost.io',
    admin_api_key: 'your_admin_api_key'
  })
});

// Upload blog post
const uploadResponse = await fetch('/api/ghost-blog-upload/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token',
    'brand': 'brand_id',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My Blog Post',
    content: '<p>Blog content here</p>',
    status: 'published'
  })
});
```

## Error Handling

All endpoints return standardized responses:

```json
{
  "status": true/false,
  "message": "Success/Error message",
  "data": {} // Response data (if applicable)
}
```

## Requirements

- PyJWT (for Ghost API authentication)
- requests (for HTTP requests)
- Django REST Framework

## Notes

- Ghost Admin API uses JWT authentication with a 5-minute expiration
- Featured images are automatically uploaded to Ghost before creating posts
- Tags are automatically created if they don't exist
- Posts can be created as drafts or published directly
