# Hashnode Blog Platform Integration

This module provides complete integration with Hashnode blog platform for uploading and managing blog posts using GraphQL API.

## Features

- **Authentication**: Connect to Hashnode using Personal Access Token
- **Blog Upload**: Upload blog posts with markdown content, tags, and metadata
- **Analytics**: Retrieve blog statistics, user info, and publication data
- **Publications**: Support for multiple publications per user
- **Management**: Create, update, and manage blog posts

## API Endpoints

### Authentication

#### Connect Hashnode Blog
```
POST /api/hashnode-auth/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>

Body:
{
  "access_token": "your_hashnode_access_token",
  "publication_id": "optional_default_publication_id"
}
```

#### Disconnect Hashnode Blog
```
POST /api/disconnect-hashnode/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>
```

#### Get Hashnode Analytics
```
GET /api/hashnode-analytics/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>
```

### Blog Management

#### Upload Blog Post
```
POST /api/hashnode-blog-upload/
Headers:
  Authorization: Bearer <token>
  brand: <brand_id>

Body:
{
  "title": "Blog Post Title",
  "content": "Blog post content in **markdown**",
  "subtitle": "Optional subtitle",
  "tags": ["javascript", "webdev"],
  "publish_as": "draft", // or "published"
  "cover_image_url": "https://example.com/image.jpg",
  "slug": "custom-slug",
  "meta_title": "SEO Title",
  "meta_description": "SEO Description",
  "publication_id": "publication_id_to_publish_to"
}
```

## Setup Instructions

### 1. Get Hashnode Personal Access Token

1. **Login to Hashnode**
   - Go to [hashnode.com](https://hashnode.com)
   - Login to your account

2. **Generate Access Token**
   - Go to [Account Settings](https://hashnode.com/settings/developer)
   - Click on "Developer" tab
   - Click "Generate New Token"
   - Give it a name (e.g., "Flowkar Integration")
   - Copy the generated token

3. **Get Publication ID (Optional)**
   - Go to your publication dashboard
   - The publication ID is in the URL or can be retrieved via API

### 2. Connect Your Blog

Use the `/api/hashnode-auth/` endpoint with your access token.

### 3. Upload Blog Posts

Use the `/api/hashnode-blog-upload/` endpoint to upload blog posts.

## Database Fields

The following fields are added to the `ThirdPartyAuth` model:

- `hashnode_check`: Boolean indicating if Hashnode is connected
- `hashnode_access_token`: Hashnode Personal Access Token
- `hashnode_username`: Hashnode username
- `hashnode_publication_id`: Default publication ID for posts

## File Structure

```
hashnode/
├── __init__.py
├── hashnode_auth.py          # Authentication handling
├── hashnode_upload.py        # Blog upload functionality
├── hashnode_analytics.py     # Analytics and statistics
└── README.md                # This documentation
```

## Usage Examples

### Python Usage

```python
from hashnode.hashnode_upload import upload_hashnode_blog

# Upload a blog post
success, message, post_data = upload_hashnode_blog(
    access_token="your_access_token",
    publication_id="your_publication_id",
    title="My Blog Post",
    content="# Hello World\n\nThis is my **first** blog post!",
    tags=["javascript", "tutorial"],
    publish_as="published"
)
```

### API Usage

```javascript
// Connect Hashnode blog
const response = await fetch('/api/hashnode-auth/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token',
    'brand': 'brand_id',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    access_token: 'your_hashnode_access_token',
    publication_id: 'optional_publication_id'
  })
});

// Upload blog post
const uploadResponse = await fetch('/api/hashnode-blog-upload/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token',
    'brand': 'brand_id',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My Blog Post',
    content: '# Hello World\n\nThis is my **first** blog post!',
    tags: ['javascript', 'tutorial'],
    publish_as: 'published',
    publication_id: 'your_publication_id'
  })
});
```

## Error Handling

All endpoints return standardized responses:

```json
{
  "status": true/false,
  "message": "Success/Error message",
  "data": {} // Response data (if applicable)
}
```

## GraphQL Queries

The integration uses Hashnode's GraphQL API with queries like:

- `publishPost` - Create new blog posts
- `updatePost` - Update existing posts
- `me` - Get user information and publications
- `publication` - Get publication details and posts

## Requirements

- requests (for HTTP requests)
- Django REST Framework

## Notes

- Hashnode uses GraphQL API instead of REST
- Content should be in Markdown format
- Tags are automatically created if they don't exist
- Publications can be personal blogs or team blogs
- Posts can be created as drafts or published directly
- Cover images should be publicly accessible URLs
- Slugs are auto-generated if not provided

## Hashnode Features Supported

- ✅ Personal Access Token authentication
- ✅ Multiple publications support
- ✅ Markdown content
- ✅ Tags and metadata
- ✅ Cover images
- ✅ Custom slugs
- ✅ SEO meta tags
- ✅ Draft/Published status
- ✅ Analytics and statistics
- ✅ User and publication info
