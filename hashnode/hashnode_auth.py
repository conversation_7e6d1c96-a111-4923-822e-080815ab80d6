"""
Hashnode Authentication Module
Handles authentication and connection validation for Hashnode API
"""

import requests
import json
from typing import <PERSON>ple, Optional, Dict, Any


class HashnodeAuth:
    """Hashnode authentication handler"""
    
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.api_url = "https://gql.hashnode.com"
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
    
    def get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        return self.headers
    
    def test_connection(self) -> Tuple[bool, str, Optional[Dict]]:
        """Test Hashnode API connection and get user info"""
        try:
            query = """
            query Me {
                me {
                    id
                    username
                    name
                    email
                    publications(first: 10) {
                        edges {
                            node {
                                id
                                title
                                url
                                displayTitle
                                descriptionSEO
                            }
                        }
                    }
                }
            }
            """
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={'query': query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    return False, f"GraphQL Error: {data['errors'][0]['message']}", None
                
                user_data = data.get('data', {}).get('me')
                if user_data:
                    return True, "Connection successful", user_data
                else:
                    return False, "Invalid response from Hashnode API", None
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except requests.exceptions.RequestException as e:
            return False, f"Connection error: {str(e)}", None
        except Exception as e:
            return False, f"Unexpected error: {str(e)}", None
    
    def get_user_publications(self) -> Tuple[bool, str, Optional[list]]:
        """Get user's publications"""
        try:
            query = """
            query Me {
                me {
                    publications(first: 20) {
                        edges {
                            node {
                                id
                                title
                                url
                                displayTitle
                                descriptionSEO
                                isTeam
                                favicon
                                headerColor
                            }
                        }
                    }
                }
            }
            """
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={'query': query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    return False, f"GraphQL Error: {data['errors'][0]['message']}", None
                
                publications = data.get('data', {}).get('me', {}).get('publications', {}).get('edges', [])
                publication_list = [edge['node'] for edge in publications]
                
                return True, "Publications retrieved successfully", publication_list
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except Exception as e:
            return False, f"Error retrieving publications: {str(e)}", None


def validate_hashnode_credentials(access_token: str) -> Tuple[bool, str, Optional[Dict]]:
    """
    Validate Hashnode credentials
    
    Args:
        access_token: Hashnode access token
        
    Returns:
        Tuple of (success, message, user_data)
    """
    if not access_token:
        return False, "Access token is required", None
    
    try:
        auth = HashnodeAuth(access_token)
        return auth.test_connection()
    except Exception as e:
        return False, f"Validation error: {str(e)}", None


def get_hashnode_user_info(access_token: str) -> Tuple[bool, str, Optional[Dict]]:
    """
    Get Hashnode user information
    
    Args:
        access_token: Hashnode access token
        
    Returns:
        Tuple of (success, message, user_info)
    """
    try:
        auth = HashnodeAuth(access_token)
        success, message, user_data = auth.test_connection()
        
        if success and user_data:
            # Get publications as well
            pub_success, pub_message, publications = auth.get_user_publications()
            if pub_success:
                user_data['publications'] = publications
            
            return True, "User info retrieved successfully", user_data
        else:
            return False, message, None
            
    except Exception as e:
        return False, f"Error getting user info: {str(e)}", None
