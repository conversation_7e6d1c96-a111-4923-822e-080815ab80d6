"""
Hashnode Analytics Module
Handles analytics and statistics retrieval from Hashnode
"""

import requests
from typing import <PERSON>ple, Optional, Dict, Any, List
from .hashnode_auth import HashnodeAuth


class HashnodeAnalytics:
    """Hashnode analytics handler"""
    
    def __init__(self, access_token: str):
        self.auth = HashnodeAuth(access_token)
        self.api_url = "https://gql.hashnode.com"
    
    def get_user_stats(self) -> <PERSON><PERSON>[bool, str, Optional[Dict]]:
        """Get user statistics"""
        try:
            query = """
            query Me {
                me {
                    id
                    username
                    name
                    followersCount
                    followingsCount
                    numPosts
                    numReactions
                    publications(first: 10) {
                        edges {
                            node {
                                id
                                title
                                url
                                posts(first: 1) {
                                    totalDocuments
                                }
                                followers(first: 1) {
                                    totalDocuments
                                }
                            }
                        }
                    }
                }
            }
            """
            
            response = requests.post(
                self.api_url,
                headers=self.auth.get_headers(),
                json={'query': query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    return False, f"GraphQL Error: {data['errors'][0]['message']}", None
                
                user_data = data.get('data', {}).get('me')
                if user_data:
                    # Calculate total stats across publications
                    total_posts = 0
                    total_followers = 0
                    publications = user_data.get('publications', {}).get('edges', [])
                    
                    for pub_edge in publications:
                        pub = pub_edge['node']
                        total_posts += pub.get('posts', {}).get('totalDocuments', 0)
                        total_followers += pub.get('followers', {}).get('totalDocuments', 0)
                    
                    stats = {
                        'user_id': user_data.get('id'),
                        'username': user_data.get('username'),
                        'name': user_data.get('name'),
                        'personal_followers': user_data.get('followersCount', 0),
                        'following': user_data.get('followingsCount', 0),
                        'personal_posts': user_data.get('numPosts', 0),
                        'total_reactions': user_data.get('numReactions', 0),
                        'publication_posts': total_posts,
                        'publication_followers': total_followers,
                        'total_publications': len(publications)
                    }
                    
                    return True, "User stats retrieved successfully", stats
                else:
                    return False, "Invalid response from Hashnode API", None
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except Exception as e:
            return False, f"Error retrieving user stats: {str(e)}", None
    
    def get_publication_stats(self, publication_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """Get publication statistics"""
        try:
            query = """
            query GetPublication($id: ObjectId!) {
                publication(id: $id) {
                    id
                    title
                    url
                    displayTitle
                    descriptionSEO
                    posts(first: 1) {
                        totalDocuments
                    }
                    followers(first: 1) {
                        totalDocuments
                    }
                    author {
                        id
                        username
                        name
                    }
                    isTeam
                    favicon
                    headerColor
                }
            }
            """
            
            variables = {"id": publication_id}
            
            response = requests.post(
                self.api_url,
                headers=self.auth.get_headers(),
                json={
                    'query': query,
                    'variables': variables
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    return False, f"GraphQL Error: {data['errors'][0]['message']}", None
                
                pub_data = data.get('data', {}).get('publication')
                if pub_data:
                    stats = {
                        'publication_id': pub_data.get('id'),
                        'title': pub_data.get('title'),
                        'display_title': pub_data.get('displayTitle'),
                        'url': pub_data.get('url'),
                        'description': pub_data.get('descriptionSEO'),
                        'total_posts': pub_data.get('posts', {}).get('totalDocuments', 0),
                        'total_followers': pub_data.get('followers', {}).get('totalDocuments', 0),
                        'is_team': pub_data.get('isTeam', False),
                        'author': pub_data.get('author', {}),
                        'favicon': pub_data.get('favicon'),
                        'header_color': pub_data.get('headerColor')
                    }
                    
                    return True, "Publication stats retrieved successfully", stats
                else:
                    return False, "Publication not found", None
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except Exception as e:
            return False, f"Error retrieving publication stats: {str(e)}", None
    
    def get_recent_posts(self, publication_id: str, first: int = 10) -> Tuple[bool, str, Optional[List]]:
        """Get recent posts from a publication"""
        try:
            query = """
            query GetRecentPosts($publicationId: ObjectId!, $first: Int!) {
                publication(id: $publicationId) {
                    posts(first: $first) {
                        edges {
                            node {
                                id
                                title
                                slug
                                url
                                publishedAt
                                updatedAt
                                brief
                                readTimeInMinutes
                                views
                                reactionCount
                                responseCount
                                author {
                                    id
                                    username
                                    name
                                }
                                tags {
                                    id
                                    name
                                    slug
                                }
                                coverImage {
                                    url
                                }
                            }
                        }
                    }
                }
            }
            """
            
            variables = {
                "publicationId": publication_id,
                "first": first
            }
            
            response = requests.post(
                self.api_url,
                headers=self.auth.get_headers(),
                json={
                    'query': query,
                    'variables': variables
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    return False, f"GraphQL Error: {data['errors'][0]['message']}", None
                
                posts_data = data.get('data', {}).get('publication', {}).get('posts', {}).get('edges', [])
                posts_list = [edge['node'] for edge in posts_data]
                
                return True, "Recent posts retrieved successfully", posts_list
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except Exception as e:
            return False, f"Error retrieving recent posts: {str(e)}", None
    
    def get_popular_tags(self, publication_id: str) -> Tuple[bool, str, Optional[List]]:
        """Get popular tags from a publication"""
        try:
            # Get recent posts and extract tags
            success, message, posts = self.get_recent_posts(publication_id, 50)
            
            if not success:
                return False, message, None
            
            # Count tag usage
            tag_counts = {}
            for post in posts:
                for tag in post.get('tags', []):
                    tag_name = tag.get('name', '')
                    if tag_name:
                        tag_counts[tag_name] = tag_counts.get(tag_name, 0) + 1
            
            # Sort by usage count
            popular_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
            
            # Format response
            tags_list = [
                {
                    'name': tag_name,
                    'count': count
                }
                for tag_name, count in popular_tags[:20]  # Top 20 tags
            ]
            
            return True, "Popular tags retrieved successfully", tags_list
            
        except Exception as e:
            return False, f"Error retrieving popular tags: {str(e)}", None


def get_hashnode_analytics(access_token: str, publication_id: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
    """
    Get comprehensive Hashnode analytics
    
    Args:
        access_token: Hashnode access token
        publication_id: Optional publication ID for specific publication stats
        
    Returns:
        Tuple of (success, message, analytics_data)
    """
    try:
        analytics = HashnodeAnalytics(access_token)
        
        # Get user stats
        user_success, user_message, user_stats = analytics.get_user_stats()
        if not user_success:
            return False, user_message, None
        
        result = {
            'user_stats': user_stats,
            'publication_stats': None,
            'recent_posts': None,
            'popular_tags': None
        }
        
        # Get publication-specific stats if publication_id provided
        if publication_id:
            pub_success, pub_message, pub_stats = analytics.get_publication_stats(publication_id)
            if pub_success:
                result['publication_stats'] = pub_stats
            
            # Get recent posts
            posts_success, posts_message, recent_posts = analytics.get_recent_posts(publication_id, 10)
            if posts_success:
                result['recent_posts'] = recent_posts
            
            # Get popular tags
            tags_success, tags_message, popular_tags = analytics.get_popular_tags(publication_id)
            if tags_success:
                result['popular_tags'] = popular_tags
        
        return True, "Analytics retrieved successfully", result
        
    except Exception as e:
        return False, f"Analytics error: {str(e)}", None
