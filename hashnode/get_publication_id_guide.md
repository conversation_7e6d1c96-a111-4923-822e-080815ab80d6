# How to Get Hashnode Publication ID

## Method 1: Using API Endpoint (Recommended)

Use the new API endpoint to get all your publications:

```bash
POST /api/hashnode-publications/
Headers:
  Authorization: Bearer <your_token>
  Content-Type: application/json

Body:
{
  "access_token": "your_hashnode_access_token"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Publications retrieved successfully",
  "data": {
    "publications": [
      {
        "id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "title": "My Tech Blog",
        "url": "https://myblog.hashnode.dev",
        "displayTitle": "My Tech Blog",
        "descriptionSEO": "A blog about technology"
      }
    ]
  }
}
```

The `id` field is your publication ID.

## Method 2: From Hashnode Dashboard

1. **Login to Hashnode**
   - Go to [hashnode.com](https://hashnode.com)
   - Login to your account

2. **Go to Publication Settings**
   - Click on your publication/blog
   - Go to Settings → General
   - Look for "Publication ID" or check the URL

3. **From URL**
   - Your publication URL: `https://yourname.hashnode.dev`
   - The publication ID is usually in the dashboard URL

## Method 3: Using GraphQL Playground

1. **Go to Hashnode GraphQL Playground**
   - Visit: [https://gql.hashnode.com](https://gql.hashnode.com)

2. **Add Authorization Header**
   ```json
   {
     "Authorization": "your_access_token"
   }
   ```

3. **Run Query**
   ```graphql
   query Me {
     me {
       id
       username
       name
       publications(first: 10) {
         edges {
           node {
             id
             title
             url
             displayTitle
             descriptionSEO
           }
         }
       }
     }
   }
   ```

4. **Get Publication ID**
   - The response will show all your publications
   - Copy the `id` field of the publication you want to use

## Method 4: From Browser Developer Tools

1. **Open Your Hashnode Blog**
   - Go to your blog (e.g., `https://yourname.hashnode.dev`)

2. **Open Developer Tools**
   - Press F12 or right-click → Inspect

3. **Go to Network Tab**
   - Refresh the page
   - Look for GraphQL requests
   - Find requests that include publication data
   - The publication ID will be in the response

## Method 5: Default Personal Blog

If you have a personal Hashnode blog (not a team publication), you can often use your username or find it in your profile settings.

## Example Usage in Code

Once you have the publication ID, you can use it in your blog upload:

```json
POST /api/hashnode-blog-upload/
{
  "title": "My Blog Post",
  "content": "# Hello World\n\nThis is my blog content!",
  "publication_id": "64f8a1b2c3d4e5f6a7b8c9d0",
  "publish_as": "published"
}
```

## Tips

1. **Save Publication ID**: Once you get it, save it in your app settings
2. **Multiple Publications**: You can have multiple publications, each with its own ID
3. **Team Publications**: If you're part of a team publication, you'll need that specific ID
4. **Personal vs Team**: Personal blogs and team publications have different IDs

## Common Publication ID Formats

Hashnode publication IDs are typically:
- MongoDB ObjectId format: `64f8a1b2c3d4e5f6a7b8c9d0` (24 characters)
- Alphanumeric string
- Case-sensitive

## Troubleshooting

**If you can't find your publication ID:**

1. **Check if you have a publication**
   - Some users might not have created a publication yet
   - Go to Hashnode dashboard and create one

2. **Verify access token**
   - Make sure your access token has the right permissions
   - Try the `/api/hashnode-publications/` endpoint

3. **Check publication ownership**
   - You can only access publications you own or are a member of

4. **Contact support**
   - If still having issues, check Hashnode documentation or support
