"""
Hashnode Blog Upload Modu<PERSON>
Handles blog post creation and management on Hashnode
"""

import requests
import json
from typing import Tuple, Optional, Dict, Any, List
from .hashnode_auth import HashnodeAuth


class HashnodeBlogUploader:
    """Hashnode blog uploader"""
    
    def __init__(self, access_token: str):
        self.auth = HashnodeAuth(access_token)
        self.api_url = "https://gql.hashnode.com"
    
    def create_post(self, 
                   publication_id: str,
                   title: str, 
                   content: str,
                   subtitle: Optional[str] = None,
                   tags: Optional[List[str]] = None,
                   cover_image_url: Optional[str] = None,
                   publish_as: str = "draft",
                   slug: Optional[str] = None,
                   meta_title: Optional[str] = None,
                   meta_description: Optional[str] = None,
                   disable_comments: bool = False) -> Tuple[bool, str, Optional[Dict]]:
        """
        Create a new blog post on Hashnode
        
        Args:
            publication_id: ID of the publication to publish to
            title: Post title
            content: Post content in markdown
            subtitle: Post subtitle
            tags: List of tag names
            cover_image_url: URL of cover image
            publish_as: "draft" or "published"
            slug: Custom slug for the post
            meta_title: SEO meta title
            meta_description: SEO meta description
            disable_comments: Whether to disable comments
            
        Returns:
            Tuple of (success, message, post_data)
        """
        try:
            # Prepare tags input
            tags_input = []
            if tags:
                for tag in tags:
                    tags_input.append({"name": tag.strip()})
            
            # Prepare the mutation
            mutation = """
            mutation PublishPost($input: PublishPostInput!) {
                publishPost(input: $input) {
                    post {
                        id
                        title
                        slug
                        url
                        publishedAt
                        updatedAt
                        brief
                        readTimeInMinutes
                        views
                        reactionCount
                        responseCount
                        publication {
                            id
                            title
                            url
                        }
                        author {
                            id
                            username
                            name
                        }
                        tags {
                            id
                            name
                            slug
                        }
                        coverImage {
                            url
                        }
                    }
                }
            }
            """
            
            # Prepare variables
            variables = {
                "input": {
                    "title": title,
                    "contentMarkdown": content,
                    "publicationId": publication_id,
                    "tags": tags_input
                }
            }
            
            # Add optional fields
            if subtitle:
                variables["input"]["subtitle"] = subtitle
            
            if cover_image_url:
                variables["input"]["coverImageOptions"] = {
                    "coverImageURL": cover_image_url
                }
            
            if slug:
                variables["input"]["slug"] = slug
            
            if meta_title:
                variables["input"]["metaTags"] = {
                    "title": meta_title
                }
                if meta_description:
                    variables["input"]["metaTags"]["description"] = meta_description
            elif meta_description:
                variables["input"]["metaTags"] = {
                    "description": meta_description
                }
            
            if disable_comments:
                variables["input"]["disableComments"] = True
            
            # Set publish status
            if publish_as.lower() == "published":
                variables["input"]["publishedAt"] = None  # Publish immediately
            # For draft, we don't set publishedAt
            
            response = requests.post(
                self.api_url,
                headers=self.auth.get_headers(),
                json={
                    'query': mutation,
                    'variables': variables
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    error_msg = data['errors'][0]['message']
                    return False, f"GraphQL Error: {error_msg}", None
                
                post_data = data.get('data', {}).get('publishPost', {}).get('post')
                if post_data:
                    return True, "Post created successfully", post_data
                else:
                    return False, "Invalid response from Hashnode API", None
            else:
                return False, f"HTTP Error: {response.status_code} - {response.text}", None
                
        except Exception as e:
            return False, f"Error creating post: {str(e)}", None
    
    def update_post(self, 
                   post_id: str,
                   title: Optional[str] = None,
                   content: Optional[str] = None,
                   subtitle: Optional[str] = None,
                   tags: Optional[List[str]] = None,
                   cover_image_url: Optional[str] = None,
                   slug: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        Update an existing blog post
        
        Args:
            post_id: ID of the post to update
            title: New title
            content: New content in markdown
            subtitle: New subtitle
            tags: New list of tag names
            cover_image_url: New cover image URL
            slug: New slug
            
        Returns:
            Tuple of (success, message, post_data)
        """
        try:
            mutation = """
            mutation UpdatePost($input: UpdatePostInput!) {
                updatePost(input: $input) {
                    post {
                        id
                        title
                        slug
                        url
                        updatedAt
                        brief
                        tags {
                            id
                            name
                            slug
                        }
                        coverImage {
                            url
                        }
                    }
                }
            }
            """
            
            variables = {
                "input": {
                    "id": post_id
                }
            }
            
            # Add fields to update
            if title:
                variables["input"]["title"] = title
            
            if content:
                variables["input"]["contentMarkdown"] = content
            
            if subtitle:
                variables["input"]["subtitle"] = subtitle
            
            if tags:
                tags_input = [{"name": tag.strip()} for tag in tags]
                variables["input"]["tags"] = tags_input
            
            if cover_image_url:
                variables["input"]["coverImageOptions"] = {
                    "coverImageURL": cover_image_url
                }
            
            if slug:
                variables["input"]["slug"] = slug
            
            response = requests.post(
                self.api_url,
                headers=self.auth.get_headers(),
                json={
                    'query': mutation,
                    'variables': variables
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    error_msg = data['errors'][0]['message']
                    return False, f"GraphQL Error: {error_msg}", None
                
                post_data = data.get('data', {}).get('updatePost', {}).get('post')
                if post_data:
                    return True, "Post updated successfully", post_data
                else:
                    return False, "Invalid response from Hashnode API", None
            else:
                return False, f"HTTP Error: {response.status_code}", None
                
        except Exception as e:
            return False, f"Error updating post: {str(e)}", None


def upload_hashnode_blog(access_token: str,
                        publication_id: str,
                        title: str,
                        content: str,
                        subtitle: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        cover_image_url: Optional[str] = None,
                        publish_as: str = "draft",
                        slug: Optional[str] = None,
                        meta_title: Optional[str] = None,
                        meta_description: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
    """
    Upload a blog post to Hashnode
    
    Args:
        access_token: Hashnode access token
        publication_id: ID of the publication to publish to
        title: Post title
        content: Post content in markdown
        subtitle: Post subtitle
        tags: List of tag names
        cover_image_url: URL of cover image
        publish_as: "draft" or "published"
        slug: Custom slug for the post
        meta_title: SEO meta title
        meta_description: SEO meta description
        
    Returns:
        Tuple of (success, message, post_data)
    """
    try:
        uploader = HashnodeBlogUploader(access_token)
        return uploader.create_post(
            publication_id=publication_id,
            title=title,
            content=content,
            subtitle=subtitle,
            tags=tags,
            cover_image_url=cover_image_url,
            publish_as=publish_as,
            slug=slug,
            meta_title=meta_title,
            meta_description=meta_description
        )
    except Exception as e:
        return False, f"Upload error: {str(e)}", None
