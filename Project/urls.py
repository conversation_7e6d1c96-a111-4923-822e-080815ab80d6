from django.urls import path

from X.x_auth import*
from .views import *

urlpatterns = [
    path('upload-post/', PostCreateView.as_view()),
    path('get-draft-post/', DraftPostsView.as_view()),
    path('draft-post-upload/', UpdatePostStatusView.as_view()),
    path('draft-delete/<id>', DeleteDraftView.as_view()),
    path('update-post/', UpdatePostView.as_view()),
    path('update-text-post/', UpdateTextPostView.as_view()),
    path('get-post/', GetSinglePostView.as_view()),
    path('get-post-web/', GetSinglePostWebView.as_view()),
    path('get-all-post/', GetPostView.as_view()),
    path('delete-post/', DeletePostView.as_view()),
    path('analytics-data/', AnalyticsView.as_view()),
    path('get-tagged-post/', TaggedUserPostView.as_view()),
    path('get-reel-post/', GetReelsView.as_view()),
    path('get-user-reel-post/', GetUserReelsView.as_view()),
    path('upload-story/', StoryCreateView.as_view()),
    path('get-story/', StoryGetView.as_view()),
    path('get-story-test/', StoryGetAllView.as_view()),
    path('delete-story/', StoryDeleteView.as_view()),
    path('check-story/', CheckStoryView.as_view()),
    path('like-unlike-story/<id>/', LikeUnlikeStoryView.as_view()),
    path('follow/', FollowView.as_view()),
    path('unfollow/', UnfollowView.as_view()),
    path('follow-list/', FollowListView.as_view()),
    path('following-list/', FollowingListView.as_view()),
    path('follow-pending-inward/', FollowPendingInwordView.as_view()),
    path('follow-pending-outward/', FollowPendingOutwordView.as_view()),
    path('like-post/', LikePostView.as_view()),
    path('like-list-post/', LikePostListView.as_view()),  
    path('comment/<id>', CommentView.as_view()),
    path('comment-like-unlike/<id>', LikeCommentView.as_view()),
    path('comment-reply/<id>', CommentReplyView.as_view()),
    path('comment-reply-like-unlike/<id>', LikeCommentReplyView.as_view()),
    path('hide-post/',HidePostView.as_view()),
    path('upload-scheduled-post/',UploadScheduledPost.as_view()),
    path('report-user/',ReportUserView.as_view()),
    path('report-post/',ReportPostView.as_view()),
    path('scheduled-posts/', ScheduledPostApiView.as_view()),
    path('scheduled-posts-web/', ScheduledPostWebApiView.as_view()),
    path('today-scheduled-posts/', TodayScheduledPostsView.as_view()),
    path('remove-scheduled-posts/', RemoveScheduledPostApiView.as_view()),
    path('save-post/', SavePostView.as_view()),
    path('web-list/', ScheduledAndPostedPostWebApiView.as_view()),
    path('web-delete-post/', PostDeleteOrUnscheduledView.as_view()),

    #ADMIN
    path('admim-dashboard/',SuperAdminDashBoardView.as_view()),
    path('user-admim-dashboard/',UserDashboardView.as_view()),
    path('api-user/', UserManagementView.as_view()),
    path('delete-user-admin/', DeleteUserView.as_view()),
    path('api-post/', PostManagementView.as_view()),
    path('delete-post-admin/', DeletePostAdminView.as_view()),

    #NOTIFICATION
    path('notification/',GetNotificationView.as_view()),
    path('notification-delete/',DeleteNotificationView.as_view()),
    path('onesignal-id/',OneSignalView.as_view()),

    #CHAT
    path('chat-list/',ChatListView.as_view()),
    path('chat-message-list/',MessageListView.as_view()),
    path('delete-chat-list/',DeleteChatListApi.as_view()),

    #SHARE PROFILE
    path('share-profile/',ShareProfileView.as_view()),
    path('share-profile-qr/',GetShareProfileQRView.as_view()),
    path('youtube-upload/',UploadYoutubeVideo.as_view()),
    path('insta-profile/',InstaProfile.as_view()),
    path('threads-profile/',ThreadsProfile.as_view()),

    #HASHTAG POST
    path('hashtag-posts/',HastagPostView.as_view()),

    #HIGHLIGHT
    path('get-Story-Highlighting/',GetStoryHighlightingView.as_view()),
    path('highlights-create/', HighlightCreateView.as_view()),
    path('highlights-list/', HighlightListView.as_view()),
    path('highlight-view/', HighlightGetAllView.as_view()),
    path('highlights-update/', HighlightUpdateView.as_view()),
    path('highlights-delete/', HighlightDeleteView.as_view()),

    #SOCIAL PLATFORMS
    path('social-platforms/', GetSocialPlatforms.as_view()),

    #FEEDBACK

    path('feedback/', FeedbackView.as_view()),
    path('load-data/', LoadDataView.as_view()),
    path('demography-list/', ListCountryCityStateView.as_view()),

    #Chat

    path('all-chat-list/',ThirdPartyChatListMessageView.as_view()),
    path('single-chat-details/',GetDetailedMessagesView.as_view()),
    path('send-instagram-message/',SendInstagramMessageView.as_view()),
    path('send-facebook-message/',SendFacebookMessageView.as_view()),

    #Search Places
    path('search-location/',SearchLocationView.as_view()),

    #Live Stream 
    path('live-users/',GetLiveUsersListView.as_view()),

    #Analytics 

    #Linkedin
    path('linkedin-analytics-g1/',LinkedInAnalyticsViewGraphOne.as_view()),
    path('linkedin-analytics-g2/',LinkedInAnalyticsViewGraphtwo.as_view()),
    path('linkedin-analytics-g3/',LinkedInAnalyticsViewGraphthree.as_view()),


    #Instagram
    path('instagram-analytics-g1/',InstagramAnalyticsDataView.as_view()),
    path('instagram-analytics-g2/',InstagramDemographicDataView.as_view()),

    #Facebook


    path('facebook-analytics-g1/',FacebookAnalyticsGraphOne.as_view()),
    path('facebook-analytics-g2/',FacebookAnalyticsGraphTwo.as_view()),


    # Youtube
    path('youtube-analytics-g1/', YouTubeVideoCountView.as_view()),
    path('youtube-analytics-g2/', YouTubeAnalyticsView.as_view()),


    # Pinterest
    path('pinterest-analytics-g1/', PinterestAnalyticsGraphOne.as_view()),
    path('pinterest-analytics-g2/', PinterestAnalyticsGraphTwo.as_view()),


    # Threads
    path('threads-analytics-g1/', ThreadsAnalyticsOne.as_view()),
    path('threads-analytics-g2/', ThreadsAnalyticsTwo.as_view()),
    path('threads-analytics-g3/', ThreadsAnalyticsThree.as_view()),
    path('threads-analytics-g4/', ThreadsAnalyticsFour.as_view()),
    path('threads-analytics-g5/', ThreadsAnalyticsFive.as_view()),

    # Mastodon
    path('mastodon-analytics/', MastodonAnalyticsPostCountView.as_view()),

    # Industry
    path('post-industry/',IndustryListView.as_view()),



    path('share/', SharePostView.as_view()),
    path('share-post-web-link/', SharePostWebLinkView.as_view()),
    # path('post/share/<id>', SharedPostView.as_view()),

    path('chat-profile-url/', ShareUserProfileView.as_view()),
    path('chat-profile-view/<id>', SharedUserProfileView.as_view()),
    path('chat-profile-deeplink/<str:user_id>', ChatProfileDeeplinkView.as_view()),

    path('share/post/<id>', SharedPostView.as_view()),
    path('post/share/<str:post_id>/', SharePostViewDeeplink.as_view()),

    path('social-connect-url/', SocialConnectUrlView.as_view()),
    path('social-connect/<id>', SocialConnectView.as_view()),
    path('social-connect-deeplink/<str:brand_id>', SocialConnectDeeplinkView.as_view()),

    path('follow-profile-url/', FollowUserProfileUrlView.as_view()),
    path('follow-profile-view/<id>', FollowUserProfileView.as_view()),
    path('follow-profile-deeplink/<str:user_id>', FollowUserProfileDeeplinkView.as_view()),

    path('comment-profile-url/', CommentUserProfileUrlView.as_view()),
    path('comment-profile-view/<id>', CommentUserProfileView.as_view()),
    path('comment-profile-deeplink/<str:post_id>', CommentUserProfileDeeplinkView.as_view()),

    path('like-profile-url/', LikeUserProfileUrlView.as_view()),
    path('like-profile-view/<id>', LikeUserProfileView.as_view()),
    path('like-profile-deeplink/<str:post_id>', LikeUserProfileDeeplinkView.as_view()),


    # path('apple-app-site-association/',AppleFileView.as_view()),
    path('assetlinks.json/',AndroifFileView.as_view()),

    path('share-post-message/',SharePostMessageView.as_view()),

    # get id address and country
    path('get-location/', CurrentLocationView.as_view()),

    #Dynamic Analytics Condition
    path('get-analytics-platforms/',CurrentAvailableAnalyticsPlatforms.as_view()),

    #Payment
    path('create-payment-link/',CreatePaymentLinkView.as_view()),

    #Reward 
    path('reward-screen/',RewardScreenView.as_view()),
    path('leaderboard/',LeaderBoardView.as_view()),
    path('get-reward-data/',GetRewardDataView.as_view()),

    #Auto Assign Current Brand
    path('auto-assign-current-brand/',AutoAssignCurrentBrandView.as_view()),

    # Telegram Integration
    path('telegram-dialogs/', TelegramDialogsView.as_view()),
    path('telegram-user-chats/', TelegramUserChatsView.as_view()),
    path('telegram-send-message/', TelegramSendMessageView.as_view()),
    path('telegram-logout/', TelegramLogoutView.as_view()),
    
    #AI
    path('ai-generate-response/',AiGenerateResponseView.as_view()),
    path('ai-generate-duplicate-post/',AiGenerateDuplicatePostView.as_view()),
    path('ai-generate-comment/',AiGenerateCommentView.as_view()),
    path('ai-generate-message-reply/',AiGenerateMessageReplyView.as_view()),

    #Post Approval
    path('pending-post-approval/',PendingPostApprovalView.as_view()),
    path('approve-post-upload/',ApprovePostUpload.as_view()),
    path('reject-post-upload/',RejectPostUpload.as_view()),

    #Test Permissions
    path('test-permissions/',TestPermissionsView.as_view()),

    #Markent
    path('create-market/',CreateMarketView.as_view()),
    path('get-market/',GetMarketView.as_view()),
    path('get-market-details/',GetMarketDetailsView.as_view()),
    path('get-markent-categories/',GetMarkentCategoriesView.as_view()),

    #KYC
    path('create-kyc-status/',CreateKYCStatusView.as_view()),
    path('create-additional-details/',CreateAdditionalDetailsView.as_view()),

    # Hashnode Blog
    path('hashnode-blog-upload/', HashnodeBlogUploadView.as_view()),
]